{"createdAt": "2021-03-04T16:23:06.210Z", "updatedAt": "2021-03-04T17:33:16.605Z", "id": "108", "name": "Merge:append keepKeyMatches mergeByIndex(leftjoin,innerjoin,outerjoin) mergeByKey(ifBlank,always,ifMissing) Multiplex passThrough removeKeyMatches", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [120, 1510], "id": "05049327-2f4e-4009-94d1-13c946fdf5ba"}, {"parameters": {}, "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "typeVersion": 1, "position": [580, 300], "id": "21ee0629-efde-4030-9a2e-6ffe5d135aab"}, {"parameters": {"values": {"boolean": [{"name": "value1", "value": true}]}, "options": {}}, "name": "Set", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [430, 220], "id": "76be9855-6e08-4898-a92f-a715ccd2d9f6"}, {"parameters": {"values": {"boolean": [], "number": [{"name": "value2", "value": 5}]}, "options": {}}, "name": "Set1", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [430, 370], "id": "49c669ce-c9d0-44ff-aabe-fc8c43b6fa5b"}, {"parameters": {"functionCode": "testData= JSON.stringify([{value1:true},{value2:5}]);\n\nif(JSON.stringify(items.map(item => item.json)) !== testData){\n  throw new Error('Error in Merge node : append');\n}\nreturn items;"}, "name": "Function", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [780, 300], "notesInFlow": true, "notes": "Verify merge operation", "id": "ee84e437-1a43-46e2-8d28-49af7bd7050b"}, {"parameters": {"mode": "keepKeyMatches", "propertyName1": "prop3", "propertyName2": "prop4"}, "name": "Merge1", "type": "n8n-nodes-base.merge", "typeVersion": 1, "position": [580, 580], "id": "76c2f8bd-920e-46d5-8ccd-c1abb9f9b107"}, {"parameters": {"values": {"boolean": [], "number": [{"name": "prop1", "value": 1}, {"name": "prop3", "value": -1}]}, "options": {}}, "name": "Set2", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [430, 500], "id": "80630a5a-6b50-4a96-8860-4ee0a44eb6d7"}, {"parameters": {"values": {"boolean": [], "number": [{"name": "prop2", "value": 2}, {"name": "prop4", "value": -1}]}, "options": {}}, "name": "Set3", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [430, 650], "id": "c711f26f-fa32-4d45-96df-64a94bda847f"}, {"parameters": {"functionCode": "testData= JSON.stringify({prop1:1,prop3:-1});\n\nif(JSON.stringify(items[0].json) !== testData){\n  throw new Error('Error in Merge node : keepKeyMatches');\n}\nreturn items;"}, "name": "Function1", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [780, 580], "notesInFlow": true, "notes": "Verify merge operation", "id": "f14b2693-ef6c-452e-8e9a-bbb0a9854108"}, {"parameters": {"mode": "mergeByIndex", "join": "inner"}, "name": "Merge2", "type": "n8n-nodes-base.merge", "typeVersion": 1, "position": [600, 900], "id": "fabcb606-0623-4637-aea2-02348c9f8395"}, {"parameters": {"functionCode": "testData= JSON.stringify([{title:'Input2item1'},{title:'Input2item2'},{title:'Input2item3'}]);\n\nif(JSON.stringify(items.map(item => item.json)) !== testData){\n  throw new Error('Error in Merge node : mergeByIndex: innerjoin');\n}\nreturn items;"}, "name": "Function2", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [750, 900], "notesInFlow": true, "notes": "Verify mergeByIndex operation", "id": "52c35538-f039-4a65-9ee0-0f8b19783e39"}, {"parameters": {"mode": "mergeByIndex"}, "name": "Merge3", "type": "n8n-nodes-base.merge", "typeVersion": 1, "position": [600, 750], "id": "a027b711-4a82-4d5c-b181-ef9c9a8f4c96"}, {"parameters": {"functionCode": "testData= JSON.stringify([{title:'Input2item1'},{title:'Input2item2'},{title:'Input2item3'}]);\n\nif(JSON.stringify(items.map(item => item.json)) !== testData){\n  throw new Error('Error in Merge node : mergeByIndex: leftjoin');\n}\nreturn items;"}, "name": "Function3", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [750, 750], "notesInFlow": true, "notes": "Verify mergeByIndex operation", "id": "637ec6e5-0166-4a3a-8590-3ba8c2772e43"}, {"parameters": {"mode": "mergeByIndex", "join": "outer"}, "name": "Merge4", "type": "n8n-nodes-base.merge", "typeVersion": 1, "position": [600, 1050], "notesInFlow": false, "notes": "outerjoin", "id": "efc5cc37-9d27-4271-b767-fd9351aadaf4"}, {"parameters": {"functionCode": "testData= JSON.stringify([{title:'Input2item1'},{title:'Input2item2'},{title:'Input2item3'},{title:'Input2item4'}]);\n\nif(JSON.stringify(items.map(item => item.json)) !== testData){\n  throw new Error('Error in Merge node : mergeByIndex: outerjoin');\n}\nreturn items;"}, "name": "Function4", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [750, 1050], "notesInFlow": true, "notes": "Verify mergeByIndex operation", "id": "00e11687-bdb1-4569-95ed-f90b199b0337"}, {"parameters": {"mode": "mergeByKey", "propertyName1": "prop3", "propertyName2": "prop4"}, "name": "Merge5", "type": "n8n-nodes-base.merge", "typeVersion": 1, "position": [580, 1380], "id": "87c14aaa-fd17-4d7b-8c05-337033d054b0"}, {"parameters": {"values": {"boolean": [], "number": [{"name": "prop2", "value": 2}, {"name": "prop3", "value": -2}, {"name": "prop4", "value": -3}]}, "options": {}}, "name": "Set6", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [400, 1440], "id": "06f1417b-2461-4aa6-92fb-46749dbf07be"}, {"parameters": {"functionCode": "testData= JSON.stringify({prop1:1,prop3:-1});\n\nif(JSON.stringify(items[0].json) !== testData){\n  throw new Error('Error in Merge node : keepKeyMatches');\n}\nreturn items;"}, "name": "Function5", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [750, 1380], "notesInFlow": true, "notes": "Verify mergeByKey operation", "id": "440e0084-64d7-48cd-8418-5ea1b0f2c4d8"}, {"parameters": {"values": {"boolean": [], "number": [{"name": "prop1", "value": 1}, {"name": "prop3", "value": -1}], "string": []}, "options": {}}, "name": "Set7", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [400, 1290], "id": "d44ca83d-c757-49c1-b35a-1f95306c796e"}, {"parameters": {"functionCode": "testData= JSON.stringify({prop1:1,prop3:-1});\n\nif(JSON.stringify(items[0].json) !== testData){\n  throw new Error('Error in Merge node : keepKeyMatches');\n}\nreturn items;"}, "name": "Function6", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [750, 1230], "notesInFlow": true, "notes": "Verify mergeByKey operation", "id": "4f20aaa3-7f0d-4dd4-a863-6c0bb2209b36"}, {"parameters": {"mode": "mergeByKey", "propertyName1": "prop3", "propertyName2": "prop4", "overwrite": "blank"}, "name": "Merge6", "type": "n8n-nodes-base.merge", "typeVersion": 1, "position": [580, 1230], "id": "ebf52dbc-51cb-4abf-a4ee-480e2c0f546c"}, {"parameters": {"functionCode": "testData= JSON.stringify({prop1:1,prop3:-1});\n\nif(JSON.stringify(items[0].json) !== testData){\n  throw new Error('Error in Merge node : keepKeyMatches');\n}\nreturn items;"}, "name": "Function7", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [750, 1530], "notesInFlow": true, "notes": "Verify mergeByKey operation", "id": "02adb59e-bbfe-442b-a185-2addbe1d1464"}, {"parameters": {"mode": "mergeByKey", "propertyName1": "prop3", "propertyName2": "prop4", "overwrite": "undefined"}, "name": "Merge7", "type": "n8n-nodes-base.merge", "typeVersion": 1, "position": [580, 1530], "id": "75a59075-beab-4a75-98b5-b6b267d3a096"}, {"parameters": {"mode": "multiplex"}, "name": "Merge8", "type": "n8n-nodes-base.merge", "typeVersion": 1, "position": [580, 1780], "id": "690e5bee-e4cc-4179-bdf6-f38ffbcd271d"}, {"parameters": {"values": {"boolean": [], "number": [{"name": "prop3", "value": 2}, {"name": "prop4", "value": -4}]}, "options": {}}, "name": "Set8", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [430, 1850], "id": "65fb39a2-10c6-4ad1-866a-a7771ac9c1af"}, {"parameters": {"functionCode": "testData= JSON.stringify({prop1: 1,prop2: -1,prop3: 2,prop4: -4});\n\nif(JSON.stringify(items[0].json) !== testData){\n  throw new Error('Error in Merge node : multiplex');\n}\nreturn items;"}, "name": "Function8", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [780, 1780], "notesInFlow": true, "notes": "Verify multiplex operation", "id": "fa2cc83d-1223-47cf-a7f8-a272a95014ab"}, {"parameters": {"values": {"boolean": [], "number": [{"name": "prop1", "value": 1}, {"name": "prop2", "value": -1}]}, "options": {}}, "name": "Set9", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [430, 1700], "id": "66c41d0d-cfb4-4422-94d6-3f2cf87eb5ae"}, {"parameters": {"mode": "passThrough"}, "name": "Merge9", "type": "n8n-nodes-base.merge", "typeVersion": 1, "position": [590, 2080], "id": "449087b4-8aac-4294-a01a-af6475eb48c1"}, {"parameters": {"values": {"boolean": [], "number": [{"name": "prop3", "value": 2}, {"name": "prop4", "value": -4}]}, "options": {}}, "name": "Set10", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [440, 2150], "id": "fe7a92b3-8f2a-408b-90df-e1b5fe647027"}, {"parameters": {"functionCode": "testData= JSON.stringify({prop1: 1,prop2: -1});\n\nif(JSON.stringify(items[0].json) !== testData){\n  throw new Error('Error in Merge node : passThrough');\n}\nreturn items;"}, "name": "Function9", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [790, 2080], "notesInFlow": true, "notes": "Verify passThrough operation", "id": "c3faf934-98ee-4bad-9326-567ad3aca34b"}, {"parameters": {"values": {"boolean": [], "number": [{"name": "prop1", "value": 1}, {"name": "prop2", "value": -1}]}, "options": {}}, "name": "Set11", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [440, 2000], "id": "48d50540-eb21-470a-9a32-5a57093ebf54"}, {"parameters": {"mode": "removeKeyMatches", "propertyName1": "prop1", "propertyName2": "prop3"}, "name": "Merge10", "type": "n8n-nodes-base.merge", "typeVersion": 1, "position": [600, 2380], "id": "8fa6f5b0-9f8f-4ff4-b15e-feaef0f92c61"}, {"parameters": {"values": {"boolean": [], "number": [{"name": "prop3", "value": 2}, {"name": "prop4", "value": -4}]}, "options": {}}, "name": "Set12", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [450, 2450], "id": "acf521b5-df53-43ff-a046-80f656dbe90b"}, {"parameters": {"values": {"boolean": [], "number": [{"name": "prop1", "value": 1}, {"name": "prop2", "value": -1}]}, "options": {}}, "name": "Set13", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [450, 2300], "id": "0310f05a-f0e6-4383-aa2a-1602dc8979e2"}, {"parameters": {"functionCode": "testData= JSON.stringify({prop1: 1,prop2: -1});\n\nif(JSON.stringify(items[0].json) !== testData){\n  throw new Error('Error in Merge node : removeKeyMatches');\n}\nreturn items;"}, "name": "Function10", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [800, 2380], "notesInFlow": true, "notes": "Verify removeKeyMatches operation", "id": "e56612ad-6b5a-4f1e-9d5c-feacc7960269"}, {"parameters": {"functionCode": "items = [{\n    json:{\n        title:'Input1item1'\n    }\n},{\n    json:{\n        title:'Input1item2'\n    }\n},{\n    json:{\n        title:'Input1item3'\n    }\n}]\nreturn items;"}, "name": "Function11", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [430, 810], "notesInFlow": true, "notes": "Input 1", "id": "96b68b9c-10fd-4d22-8836-0babe8295c53"}, {"parameters": {"functionCode": "items = [{\n    json:{\n        title:'Input2item1'\n    }\n},{\n    json:{\n        title:'Input2item2'\n    }\n},{\n    json:{\n        title:'Input2item3'\n    }\n},{\n    json:{\n        title:'Input2item4'\n    }\n}]\nreturn items;"}, "name": "Function12", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [430, 960], "notesInFlow": true, "notes": "Input 2", "id": "b1382fe4-f188-44da-aeec-cfebab259efe"}], "connections": {"Merge": {"main": [[{"node": "Function", "type": "main", "index": 0}]]}, "Set1": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Set": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Merge1": {"main": [[{"node": "Function1", "type": "main", "index": 0}]]}, "Set2": {"main": [[{"node": "Merge1", "type": "main", "index": 0}]]}, "Set3": {"main": [[{"node": "Merge1", "type": "main", "index": 1}]]}, "Merge2": {"main": [[{"node": "Function2", "type": "main", "index": 0}]]}, "Merge3": {"main": [[{"node": "Function3", "type": "main", "index": 0}]]}, "Merge4": {"main": [[{"node": "Function4", "type": "main", "index": 0}]]}, "Merge5": {"main": [[{"node": "Function5", "type": "main", "index": 0}]]}, "Set6": {"main": [[{"node": "Merge5", "type": "main", "index": 1}, {"node": "Merge6", "type": "main", "index": 1}, {"node": "Merge7", "type": "main", "index": 1}]]}, "Set7": {"main": [[{"node": "Merge5", "type": "main", "index": 0}, {"node": "Merge6", "type": "main", "index": 0}, {"node": "Merge7", "type": "main", "index": 0}]]}, "Merge6": {"main": [[{"node": "Function6", "type": "main", "index": 0}]]}, "Merge7": {"main": [[{"node": "Function7", "type": "main", "index": 0}]]}, "Merge8": {"main": [[{"node": "Function8", "type": "main", "index": 0}]]}, "Set8": {"main": [[{"node": "Merge8", "type": "main", "index": 1}]]}, "Set9": {"main": [[{"node": "Merge8", "type": "main", "index": 0}]]}, "Merge9": {"main": [[{"node": "Function9", "type": "main", "index": 0}]]}, "Set10": {"main": [[{"node": "Merge9", "type": "main", "index": 1}]]}, "Set11": {"main": [[{"node": "Merge9", "type": "main", "index": 0}]]}, "Merge10": {"main": [[{"node": "Function10", "type": "main", "index": 0}]]}, "Set12": {"main": [[{"node": "Merge10", "type": "main", "index": 1}]]}, "Set13": {"main": [[{"node": "Merge10", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "Set", "type": "main", "index": 0}, {"node": "Set1", "type": "main", "index": 0}, {"node": "Set2", "type": "main", "index": 0}, {"node": "Set3", "type": "main", "index": 0}, {"node": "Set7", "type": "main", "index": 0}, {"node": "Set6", "type": "main", "index": 0}, {"node": "Set9", "type": "main", "index": 0}, {"node": "Set8", "type": "main", "index": 0}, {"node": "Set11", "type": "main", "index": 0}, {"node": "Set10", "type": "main", "index": 0}, {"node": "Set13", "type": "main", "index": 0}, {"node": "Set12", "type": "main", "index": 0}, {"node": "Function11", "type": "main", "index": 0}, {"node": "Function12", "type": "main", "index": 0}]]}, "Function11": {"main": [[{"node": "Merge3", "type": "main", "index": 0}, {"node": "Merge2", "type": "main", "index": 0}, {"node": "Merge4", "type": "main", "index": 0}]]}, "Function12": {"main": [[{"node": "Merge3", "type": "main", "index": 1}, {"node": "Merge2", "type": "main", "index": 1}, {"node": "Merge4", "type": "main", "index": 1}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}