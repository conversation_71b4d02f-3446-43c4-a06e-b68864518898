{"name": "n8n-workflow", "version": "1.97.0", "description": "Workflow base code of n8n", "main": "dist/index.js", "module": "src/index.ts", "types": "dist/index.d.ts", "exports": {".": {"require": "./dist/index.js", "import": "./src/index.ts", "types": "./dist/index.d.ts"}, "./*": "./*"}, "scripts": {"clean": "rimraf dist .turbo", "dev": "pnpm watch", "typecheck": "tsc --noEmit", "build": "tsc -p tsconfig.build.json", "format": "biome format --write .", "format:check": "biome ci .", "lint": "eslint . --quiet", "lintfix": "eslint . --fix", "watch": "tsc -p tsconfig.build.json --watch", "test": "jest", "test:dev": "jest --watch"}, "files": ["dist/**/*"], "devDependencies": {"@langchain/core": "catalog:", "@n8n/config": "workspace:*", "@n8n/typescript-config": "workspace:*", "@types/express": "catalog:", "@types/jmespath": "^0.15.0", "@types/lodash": "catalog:", "@types/luxon": "3.2.0", "@types/md5": "^2.3.5", "@types/xml2js": "catalog:"}, "dependencies": {"@n8n/tournament": "1.0.6", "ast-types": "0.15.2", "callsites": "catalog:", "esprima-next": "5.8.4", "form-data": "catalog:", "jmespath": "0.16.0", "js-base64": "catalog:", "jssha": "3.3.1", "lodash": "catalog:", "luxon": "catalog:", "md5": "2.3.0", "recast": "0.22.0", "title-case": "3.0.3", "transliteration": "2.3.5", "xml2js": "catalog:", "zod": "catalog:"}}