{"data": {"startData": {}, "resultData": {"runData": {"Start": [{"startTime": 1705409098613, "executionTime": 0, "source": [], "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "Salesforce": [{"startTime": *************, "executionTime": 397, "source": [{"previousNode": "Start"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": "0060900000WRCqkAAH", "success": true, "errors": ["json array"]}, "pairedItem": {"item": 0}}]]}}], "Salesforce7": [{"startTime": *************, "executionTime": 152, "source": [{"previousNode": "Start"}], "executionStatus": "success", "data": {"main": [[{"json": {"attributes": {"object": true}, "Id": "0010900002NKrwVAAT", "Name": "Account1705408303842", "BillingCity": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "Sleep 0.5 second4": [{"startTime": *************, "executionTime": 517, "source": [{"previousNode": "Start"}], "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}, "index": 0}]]}}], "Sleep 0.5 second": [{"startTime": *************, "executionTime": 505, "source": [{"previousNode": "Salesforce"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": "0060900000WRCqkAAH", "success": true, "errors": ["json array"]}, "pairedItem": {"item": 0}, "index": 0}]]}}], "Salesforce8": [{"startTime": *************, "executionTime": 217, "source": [{"previousNode": "Sleep 0.5 second4"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": "00T0900001bX5nSEAS", "success": true, "errors": ["json array"]}, "pairedItem": {"item": 0}}]]}}], "Salesforce1": [{"startTime": *************, "executionTime": 169, "source": [{"previousNode": "Sleep 0.5 second"}], "executionStatus": "success", "data": {"main": [[{"json": {"attributes": {"object": true}, "Id": "0060900000WRCqkAAH", "IsDeleted": false, "AccountId": {"object": true}, "IsPrivate": false, "Name": "Opp1705409098616", "Description": {"object": true}, "StageName": "Value Proposition", "Amount": {"object": true}, "Probability": 50, "ExpectedRevenue": {"object": true}, "TotalOpportunityQuantity": {"object": true}, "CloseDate": "2021-03-31", "Type": {"object": true}, "NextStep": {"object": true}, "LeadSource": {"object": true}, "IsClosed": false, "IsWon": false, "ForecastCategory": "Pipeline", "ForecastCategoryName": "Pipeline", "CampaignId": {"object": true}, "HasOpportunityLineItem": false, "Pricebook2Id": {"object": true}, "OwnerId": "00509000005ntkGAAQ", "CreatedDate": "2024-01-16T12:44:58.000+0000", "CreatedById": "00509000005ntkGAAQ", "LastModifiedDate": "2024-01-16T12:44:58.000+0000", "LastModifiedById": "00509000005ntkGAAQ", "SystemModstamp": "2024-01-16T12:44:59.000+0000", "LastActivityDate": {"object": true}, "PushCount": 0, "LastStageChangeDate": {"object": true}, "FiscalQuarter": 1, "FiscalYear": 2021, "Fiscal": "2021 1", "ContactId": {"object": true}, "LastViewedDate": "2024-01-16T12:44:59.000+0000", "LastReferencedDate": "2024-01-16T12:44:59.000+0000", "HasOpenActivity": false, "HasOverdueTask": false, "LastAmountChangedHistoryId": {"object": true}, "LastCloseDateChangedHistoryId": {"object": true}, "DeliveryInstallationStatus__c": {"object": true}, "TrackingNumber__c": {"object": true}, "OrderNumber__c": {"object": true}, "CurrentGenerators__c": {"object": true}, "MainCompetitors__c": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "Salesforce9": [{"startTime": *************, "executionTime": 166, "source": [{"previousNode": "Salesforce8"}], "executionStatus": "success", "data": {"main": [[{"json": {"attributes": {"object": true}, "Id": "00T0900001bX5nSEAS", "WhoId": {"object": true}, "WhatId": {"object": true}, "Subject": {"object": true}, "ActivityDate": {"object": true}, "Status": "In Progress", "Priority": "Normal", "IsHighPriority": false, "OwnerId": "00509000005ntkGAAQ", "Description": {"object": true}, "IsDeleted": false, "AccountId": {"object": true}, "IsClosed": false, "CreatedDate": "2024-01-16T12:45:00.000+0000", "CreatedById": "00509000005ntkGAAQ", "LastModifiedDate": "2024-01-16T12:45:00.000+0000", "LastModifiedById": "00509000005ntkGAAQ", "SystemModstamp": "2024-01-16T12:45:00.000+0000", "IsArchived": false, "CallDurationInSeconds": {"object": true}, "CallType": {"object": true}, "CallDisposition": {"object": true}, "CallObject": {"object": true}, "ReminderDateTime": {"object": true}, "IsReminderSet": false, "RecurrenceActivityId": {"object": true}, "IsRecurrence": false, "RecurrenceStartDateOnly": {"object": true}, "RecurrenceEndDateOnly": {"object": true}, "RecurrenceTimeZoneSidKey": {"object": true}, "RecurrenceType": {"object": true}, "RecurrenceInterval": {"object": true}, "RecurrenceDayOfWeekMask": {"object": true}, "RecurrenceDayOfMonth": {"object": true}, "RecurrenceInstance": {"object": true}, "RecurrenceMonthOfYear": {"object": true}, "RecurrenceRegeneratedType": {"object": true}, "TaskSubtype": "Task", "CompletedDateTime": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "Salesforce2": [{"startTime": 1705409100741, "executionTime": 1618, "source": [{"previousNode": "Salesforce1"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": "002090000011nDqAAI", "success": true, "errors": ["json array"]}, "pairedItem": {"item": 0}}]]}}], "Sleep 0.5 second3": [{"startTime": *************, "executionTime": 505, "source": [{"previousNode": "Salesforce9"}], "executionStatus": "success", "data": {"main": [[{"json": {"attributes": {"object": true}, "Id": "00T0900001bX5nSEAS", "WhoId": {"object": true}, "WhatId": {"object": true}, "Subject": {"object": true}, "ActivityDate": {"object": true}, "Status": "In Progress", "Priority": "Normal", "IsHighPriority": false, "OwnerId": "00509000005ntkGAAQ", "Description": {"object": true}, "IsDeleted": false, "AccountId": {"object": true}, "IsClosed": false, "CreatedDate": "2024-01-16T12:45:00.000+0000", "CreatedById": "00509000005ntkGAAQ", "LastModifiedDate": "2024-01-16T12:45:00.000+0000", "LastModifiedById": "00509000005ntkGAAQ", "SystemModstamp": "2024-01-16T12:45:00.000+0000", "IsArchived": false, "CallDurationInSeconds": {"object": true}, "CallType": {"object": true}, "CallDisposition": {"object": true}, "CallObject": {"object": true}, "ReminderDateTime": {"object": true}, "IsReminderSet": false, "RecurrenceActivityId": {"object": true}, "IsRecurrence": false, "RecurrenceStartDateOnly": {"object": true}, "RecurrenceEndDateOnly": {"object": true}, "RecurrenceTimeZoneSidKey": {"object": true}, "RecurrenceType": {"object": true}, "RecurrenceInterval": {"object": true}, "RecurrenceDayOfWeekMask": {"object": true}, "RecurrenceDayOfMonth": {"object": true}, "RecurrenceInstance": {"object": true}, "RecurrenceMonthOfYear": {"object": true}, "RecurrenceRegeneratedType": {"object": true}, "TaskSubtype": "Task", "CompletedDateTime": {"object": true}}, "pairedItem": {"item": 0}, "index": 0}]]}}], "Sleep 0.5 second1": [{"startTime": 1705409102865, "executionTime": 506, "source": [{"previousNode": "Salesforce2"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": "002090000011nDqAAI", "success": true, "errors": ["json array"]}, "pairedItem": {"item": 0}, "index": 0}]]}}], "Salesforce10": [{"startTime": 1705409103371, "executionTime": 135, "source": [{"previousNode": "Sleep 0.5 second3"}], "executionStatus": "success", "data": {"main": [[{"json": {"objectDescribe": {"object": true}, "recentItems": ["json array"]}, "pairedItem": {"item": 0}}]]}}], "Salesforce3": [{"startTime": 1705409103506, "executionTime": 142, "source": [{"previousNode": "Sleep 0.5 second1"}], "executionStatus": "success", "data": {"main": [[{"json": {"objectDescribe": {"object": true}, "recentItems": ["json array"]}, "pairedItem": {"item": 0}}]]}}], "Salesforce11": [{"startTime": 1705409103648, "executionTime": 164, "source": [{"previousNode": "Salesforce10"}], "executionStatus": "success", "data": {"main": [[{"json": {"attributes": {"object": true}, "Id": "00T0900000NzAPdEAN", "Subject": {"object": true}, "Status": "In Progress", "Priority": "Normal"}, "pairedItem": {"item": 0}}]]}}], "Salesforce4": [{"startTime": *************, "executionTime": 153, "source": [{"previousNode": "Salesforce3"}], "executionStatus": "success", "data": {"main": [[{"json": {"attributes": {"object": true}, "Id": "00609000006wdbhAAA", "AccountId": {"object": true}, "Amount": {"object": true}, "Probability": 50, "Type": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "Sleep 0.5 second5": [{"startTime": *************, "executionTime": 507, "source": [{"previousNode": "Salesforce11"}], "executionStatus": "success", "data": {"main": [[{"json": {"attributes": {"object": true}, "Id": "00T0900000NzAPdEAN", "Subject": {"object": true}, "Status": "In Progress", "Priority": "Normal"}, "pairedItem": {"item": 0}, "index": 0}]]}}], "Sleep 0.5 second2": [{"startTime": *************, "executionTime": 506, "source": [{"previousNode": "Salesforce4"}], "executionStatus": "success", "data": {"main": [[{"json": {"attributes": {"object": true}, "Id": "00609000006wdbhAAA", "AccountId": {"object": true}, "Amount": {"object": true}, "Probability": 50, "Type": {"object": true}}, "pairedItem": {"item": 0}, "index": 0}]]}}], "Salesforce12": [{"startTime": *************, "executionTime": 193, "source": [{"previousNode": "Sleep 0.5 second5"}], "executionStatus": "success", "data": {"main": [[{"json": {"errors": ["json array"], "success": true}, "pairedItem": {"item": 0}}]]}}], "Salesforce5": [{"startTime": *************, "executionTime": 245, "source": [{"previousNode": "Sleep 0.5 second2"}], "executionStatus": "success", "data": {"main": [[{"json": {"errors": ["json array"], "success": true}, "pairedItem": {"item": 0}}]]}}], "Salesforce13": [{"startTime": *************, "executionTime": 358, "source": [{"previousNode": "Salesforce12"}], "executionStatus": "success", "data": {"main": [[{"json": {"errors": ["json array"], "success": true}, "pairedItem": {"item": 0}}]]}}], "Salesforce6": [{"startTime": *************, "executionTime": 409, "source": [{"previousNode": "Salesforce5"}], "executionStatus": "success", "data": {"main": [[{"json": {"errors": ["json array"], "success": true}, "pairedItem": {"item": 0}}]]}}]}, "lastNodeExecuted": "Salesforce6"}, "executionData": {"contextData": {}, "nodeExecutionStack": [], "metadata": {}, "waitingExecution": {}, "waitingExecutionSource": {}}}, "mode": "cli", "startedAt": "2024-01-16T12:44:58.611Z", "stoppedAt": "2024-01-16T12:45:06.187Z", "status": "running", "finished": true}