{"createdAt": "2021-04-09T18:07:51.949Z", "updatedAt": "2021-04-09T18:10:34.849Z", "id": "167", "name": "FreshDesk:Contact:create get update getAll delete:Ticket:create get getAll update delete", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300], "id": "30b651f9-2126-4c0c-9574-2c52f2ebddc0"}, {"parameters": {"resource": "contact", "name": "=Name{{Date.now()}}", "email": "=Fake{{Date.now()}}@email.com", "additionalFields": {"company_id": 80000582589}}, "name": "Freshdesk", "type": "n8n-nodes-base.freshdesk", "typeVersion": 1, "position": [500, 300], "credentials": {"freshdeskApi": {"id": "134", "name": "Freshdesk API creds"}}, "id": "106ad001-92d8-4969-8bbb-44f90f77b2f0"}, {"parameters": {"resource": "contact", "operation": "get", "contactId": "={{$node[\"Freshdesk\"].json[\"id\"]}}"}, "name": "Freshdesk1", "type": "n8n-nodes-base.freshdesk", "typeVersion": 1, "position": [650, 300], "credentials": {"freshdeskApi": {"id": "134", "name": "Freshdesk API creds"}}, "id": "6953c12c-ddce-45c5-89f7-65179a5b8f5a"}, {"parameters": {"resource": "contact", "operation": "update", "contactId": "={{$node[\"Freshdesk\"].json[\"id\"]}}", "additionalFields": {"job_title": "QA", "tags": ["Test"]}}, "name": "Freshdesk2", "type": "n8n-nodes-base.freshdesk", "typeVersion": 1, "position": [800, 300], "credentials": {"freshdeskApi": {"id": "134", "name": "Freshdesk API creds"}}, "id": "b31d99f6-5818-458c-98ca-17ba07e85319"}, {"parameters": {"resource": "contact", "operation": "getAll", "filters": {"email": "={{$node[\"Freshdesk1\"].json[\"email\"]}}"}}, "name": "Freshdesk3", "type": "n8n-nodes-base.freshdesk", "typeVersion": 1, "position": [950, 300], "credentials": {"freshdeskApi": {"id": "134", "name": "Freshdesk API creds"}}, "id": "67a516db-2f2e-4963-a3e2-e1c692c4d271"}, {"parameters": {"resource": "contact", "operation": "delete", "contactId": "={{$node[\"Freshdesk\"].json[\"id\"]}}"}, "name": "Freshdesk4", "type": "n8n-nodes-base.freshdesk", "typeVersion": 1, "position": [1850, 300], "credentials": {"freshdeskApi": {"id": "134", "name": "Freshdesk API creds"}}, "id": "f1658504-002f-4bd9-9323-87c9923953e2"}, {"parameters": {"requesterIdentificationValue": "={{$node[\"Freshdesk\"].json[\"id\"]}}", "priority": "medium", "options": {"agent": 80014216011, "company": 80000582589, "description": "Testing Workflow", "group": 80000368389, "name": "=TicketTest", "subject": "Testing ticket creation", "tags": "test,", "type": "Question"}}, "name": "Freshdesk5", "type": "n8n-nodes-base.freshdesk", "typeVersion": 1, "position": [1100, 400], "credentials": {"freshdeskApi": {"id": "134", "name": "Freshdesk API creds"}}, "id": "6859ca0a-615c-451e-961f-2293fbd40ae0"}, {"parameters": {"operation": "get", "ticketId": "={{$node[\"Freshdesk5\"].json[\"id\"]}}"}, "name": "Freshdesk6", "type": "n8n-nodes-base.freshdesk", "typeVersion": 1, "position": [1250, 400], "credentials": {"freshdeskApi": {"id": "134", "name": "Freshdesk API creds"}}, "id": "b20c4c3a-7bcb-4c22-995e-c4ce5f920c70"}, {"parameters": {"operation": "getAll", "limit": 1, "options": {}}, "name": "Freshdesk7", "type": "n8n-nodes-base.freshdesk", "typeVersion": 1, "position": [1400, 400], "credentials": {"freshdeskApi": {"id": "134", "name": "Freshdesk API creds"}}, "id": "988bd8e4-4b99-4612-b0cf-8e77c652fd4b"}, {"parameters": {"operation": "update", "ticketId": "={{$node[\"Freshdesk5\"].json[\"id\"]}}", "updateFields": {"status": "open", "tags": "test,updated"}}, "name": "Freshdesk8", "type": "n8n-nodes-base.freshdesk", "typeVersion": 1, "position": [1550, 400], "credentials": {"freshdeskApi": {"id": "134", "name": "Freshdesk API creds"}}, "id": "421ede3f-463e-4262-b4a3-7f874e0866c1"}, {"parameters": {"operation": "delete", "ticketId": "={{$node[\"Freshdesk5\"].json[\"id\"]}}"}, "name": "Freshdesk9", "type": "n8n-nodes-base.freshdesk", "typeVersion": 1, "position": [1700, 400], "credentials": {"freshdeskApi": {"id": "134", "name": "Freshdesk API creds"}}, "id": "2a3e7a8e-b5e8-42a4-be76-b97d1c877942"}], "connections": {"Freshdesk": {"main": [[{"node": "Freshdesk1", "type": "main", "index": 0}]]}, "Freshdesk1": {"main": [[{"node": "Freshdesk2", "type": "main", "index": 0}]]}, "Freshdesk2": {"main": [[{"node": "Freshdesk3", "type": "main", "index": 0}]]}, "Freshdesk3": {"main": [[{"node": "Freshdesk5", "type": "main", "index": 0}]]}, "Freshdesk5": {"main": [[{"node": "Freshdesk6", "type": "main", "index": 0}]]}, "Freshdesk6": {"main": [[{"node": "Freshdesk7", "type": "main", "index": 0}]]}, "Freshdesk7": {"main": [[{"node": "Freshdesk8", "type": "main", "index": 0}]]}, "Freshdesk8": {"main": [[{"node": "Freshdesk9", "type": "main", "index": 0}]]}, "Freshdesk9": {"main": [[{"node": "Freshdesk4", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "Freshdesk", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}