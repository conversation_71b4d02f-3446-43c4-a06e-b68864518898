{"data": {"startData": {}, "resultData": {"runData": {"Start": [{"startTime": 1747343995426, "executionIndex": 0, "source": [], "hints": [], "executionTime": 0, "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "Delete existing dir": [{"startTime": 1747343995426, "executionIndex": 1, "source": [{"previousNode": "Start"}], "hints": [], "executionTime": 13, "executionStatus": "success", "data": {"main": [[{"json": {"exitCode": 0, "stderr": "", "stdout": ""}, "pairedItem": {"item": 0}}]]}}], "Git": [{"startTime": 1747343995439, "executionIndex": 2, "source": [{"previousNode": "Delete existing dir"}], "hints": [], "executionTime": 515, "executionStatus": "success", "data": {"main": [[{"json": {"success": true}, "pairedItem": {"item": 0}}]]}}], "Git1": [{"startTime": 1747343995954, "executionIndex": 3, "source": [{"previousNode": "Git"}], "hints": [], "executionTime": 13, "executionStatus": "success", "data": {"main": [[{"json": {"_file": "/Library/Developer/CommandLineTools/usr/share/git-core/gitconfig"}, "pairedItem": {"item": 0}}]]}}], "Git2": [{"startTime": 1747343995967, "executionIndex": 4, "source": [{"previousNode": "Git1"}], "hints": [], "executionTime": 170, "executionStatus": "success", "data": {"main": [[{"json": {"success": true}, "pairedItem": {"item": 0}}, {"json": {"success": true}, "pairedItem": {"item": 1}}]]}}], "Git7": [{"startTime": 1747343996137, "executionIndex": 5, "source": [{"previousNode": "Git2"}], "hints": [], "executionTime": 132, "executionStatus": "success", "data": {"main": [[{"json": {"success": true}, "pairedItem": {"item": 0}}, {"json": {"success": true}, "pairedItem": {"item": 1}}]]}}], "Add file to dir": [{"startTime": 1747343996269, "executionIndex": 6, "source": [{"previousNode": "Git7"}], "hints": [], "executionTime": 5, "executionStatus": "success", "data": {"main": [[{"json": {"exitCode": 0, "stderr": "", "stdout": ""}, "pairedItem": {"item": 0}}]]}}], "Git3": [{"startTime": 1747343996274, "executionIndex": 7, "source": [{"previousNode": "Add file to dir"}], "hints": [], "executionTime": 147, "executionStatus": "success", "data": {"main": [[{"json": {"success": true}, "pairedItem": {"item": 0}}]]}}], "Git4": [{"startTime": 1747343996421, "executionIndex": 8, "source": [{"previousNode": "Git3"}], "hints": [], "executionTime": 273, "executionStatus": "success", "data": {"main": [[{"json": {"not_added": ["json array"], "conflicted": ["json array"], "created": ["json array"], "deleted": ["json array"], "modified": ["json array"], "renamed": ["json array"], "files": ["json array"], "staged": ["json array"], "ahead": 0, "behind": 0, "current": "main", "tracking": "origin/main", "detached": false}, "pairedItem": {"item": 0}}]]}}], "Git5": [{"startTime": 1747343996694, "executionIndex": 9, "source": [{"previousNode": "Git4"}], "hints": [], "executionTime": 24, "executionStatus": "success", "data": {"main": [[{"json": {"success": true}, "pairedItem": {"item": 0}}]]}}], "Git6": [{"startTime": 1747343996718, "executionIndex": 10, "source": [{"previousNode": "Git5"}], "hints": [], "executionTime": 13, "executionStatus": "success", "data": {"main": [[{"json": {"hash": "103944f38dc823d0f4a9d0720c9bdbd18528cd21", "date": "2025-05-15T22:19:56+01:00", "message": "GitNode commit Thu, 15 May 2025 21:19:56 GMT", "refs": "HEAD -> main", "body": "", "author_name": "nodemationqa", "author_email": "<EMAIL>"}, "pairedItem": {"item": 0}}]]}}], "Git8": [{"startTime": 1747343996731, "executionIndex": 11, "source": [{"previousNode": "Git6"}], "hints": [], "executionTime": 63, "executionStatus": "success", "data": {"main": [[{"json": {"success": true}, "pairedItem": {"item": 0}}]]}}], "Git9": [{"startTime": 1747343996794, "executionIndex": 12, "source": [{"previousNode": "Git8"}], "hints": [], "executionTime": 0, "executionStatus": "success", "data": {"main": [[{"json": {"success": true}, "pairedItem": {"item": 0}}]]}}], "Git10": [{"startTime": 1747343996794, "executionIndex": 13, "source": [{"previousNode": "Git9"}], "hints": [], "executionTime": 0, "executionStatus": "success", "data": {"main": [[{"json": {"success": true}, "pairedItem": {"item": 0}}]]}}], "Git11": [{"startTime": 1747343996794, "executionIndex": 14, "source": [{"previousNode": "Git10"}], "hints": [], "executionTime": 0, "executionStatus": "success", "data": {"main": [[{"json": {"success": true}, "pairedItem": {"item": 0}}]]}}], "Git12": [{"startTime": 1747343996794, "executionIndex": 15, "source": [{"previousNode": "Git11"}], "hints": [], "executionTime": 352, "executionStatus": "success", "data": {"main": [[{"json": {"success": true}, "pairedItem": {"item": 0}}]]}}], "Git13": [{"startTime": 1747343997146, "executionIndex": 16, "source": [{"previousNode": "Git12"}], "hints": [], "executionTime": 0, "executionStatus": "success", "data": {"main": [[{"json": {"success": true}, "pairedItem": {"item": 0}}]]}}]}, "lastNodeExecuted": "Git13"}, "executionData": {"contextData": {}, "nodeExecutionStack": [], "metadata": {}, "waitingExecution": {}, "waitingExecutionSource": {}}}, "mode": "cli", "startedAt": "2025-05-15T21:19:55.426Z", "stoppedAt": "2025-05-15T21:19:57.146Z", "status": "running", "finished": true}