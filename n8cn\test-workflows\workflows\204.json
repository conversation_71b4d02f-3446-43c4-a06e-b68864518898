{"createdAt": "2021-05-14T09:31:14.466Z", "updatedAt": "2021-06-08T08:48:10.045Z", "id": "204", "name": "HelpScout:Mailbox:getAll get:Customer:create get update getAll properties:Conversation:create get getAll delete:Thread:create getAll", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300], "id": "134a1cd7-83e3-4237-b289-20302a26d111"}, {"parameters": {"resource": "customer", "additionalFields": {"firstName": "=Fname_{{(new Date).toISOString()}}"}}, "name": "HelpScout", "type": "n8n-nodes-base.helpScout", "typeVersion": 1, "position": [450, 300], "credentials": {"helpScoutOAuth2Api": {"id": "163", "name": "HelpScout OAuth2 API creds"}}, "id": "d338cf1d-360e-499d-8c97-f3256b62cde4"}, {"parameters": {"resource": "customer", "operation": "get", "customerId": "={{$node[\"HelpScout\"].json[\"id\"]}}"}, "name": "HelpScout1", "type": "n8n-nodes-base.helpScout", "typeVersion": 1, "position": [600, 300], "credentials": {"helpScoutOAuth2Api": {"id": "163", "name": "HelpScout OAuth2 API creds"}}, "id": "aec33ae5-41da-476a-80b6-052bb06e6d13"}, {"parameters": {"resource": "customer", "operation": "update", "customerId": "={{$node[\"HelpScout\"].json[\"id\"]}}", "updateFields": {"jobTitle": "=UpdatedJobTitle"}}, "name": "HelpScout2", "type": "n8n-nodes-base.helpScout", "typeVersion": 1, "position": [750, 300], "credentials": {"helpScoutOAuth2Api": {"id": "163", "name": "HelpScout OAuth2 API creds"}}, "id": "895e41cb-bd06-454f-8c1f-68f632829c18"}, {"parameters": {"resource": "customer", "operation": "getAll", "limit": 1, "options": {}}, "name": "HelpScout3", "type": "n8n-nodes-base.helpScout", "typeVersion": 1, "position": [900, 300], "credentials": {"helpScoutOAuth2Api": {"id": "163", "name": "HelpScout OAuth2 API creds"}}, "id": "5b52d840-a8bd-4cb7-b1f8-f451a5be3d76"}, {"parameters": {"resource": "customer", "operation": "properties"}, "name": "HelpScout4", "type": "n8n-nodes-base.helpScout", "typeVersion": 1, "position": [1050, 300], "credentials": {"helpScoutOAuth2Api": {"id": "163", "name": "HelpScout OAuth2 API creds"}}, "id": "31ebb207-80e5-417d-89ca-a1aeef540218"}, {"parameters": {"mailboxId": 224189, "status": "active", "subject": "=Subject_{{(new Date).toISOString()}}", "type": "chat", "additionalFields": {"customerId": "={{$node[\"HelpScout\"].json[\"id\"]}}"}, "threadsUi": {"threadsValues": [{"type": "chat", "text": "Initial Thread"}]}}, "name": "HelpScout5", "type": "n8n-nodes-base.helpScout", "typeVersion": 1, "position": [600, 450], "credentials": {"helpScoutOAuth2Api": {"id": "163", "name": "HelpScout OAuth2 API creds"}}, "notes": "IGNORED_PROPERTIES=preview", "id": "dac34f58-aadc-4c27-bb53-797160518e38"}, {"parameters": {"operation": "get", "conversationId": "={{$node[\"HelpScout5\"].json[\"id\"]}}"}, "name": "HelpScout6", "type": "n8n-nodes-base.helpScout", "typeVersion": 1, "position": [750, 450], "credentials": {"helpScoutOAuth2Api": {"id": "163", "name": "HelpScout OAuth2 API creds"}}, "notes": "IGNORED_PROPERTIES=preview", "id": "54b304e7-e089-4b9e-ab0b-b8d768b2bb57"}, {"parameters": {"operation": "getAll", "limit": 1, "options": {}}, "name": "HelpScout7", "type": "n8n-nodes-base.helpScout", "typeVersion": 1, "position": [900, 450], "credentials": {"helpScoutOAuth2Api": {"id": "163", "name": "HelpScout OAuth2 API creds"}}, "id": "fef75568-538b-4ce2-83c5-07d779e6393e"}, {"parameters": {"operation": "delete", "conversationId": "={{$node[\"HelpScout5\"].json[\"id\"]}}"}, "name": "HelpScout8", "type": "n8n-nodes-base.helpScout", "typeVersion": 1, "position": [1050, 450], "credentials": {"helpScoutOAuth2Api": {"id": "163", "name": "HelpScout OAuth2 API creds"}}, "id": "b082f9a9-352c-4624-a287-c167aa820875"}, {"parameters": {"resource": "mailbox", "operation": "getAll", "limit": 1}, "name": "HelpScout9", "type": "n8n-nodes-base.helpScout", "typeVersion": 1, "position": [450, 150], "credentials": {"helpScoutOAuth2Api": {"id": "163", "name": "HelpScout OAuth2 API creds"}}, "id": "4c07019c-6818-484d-8865-ef7bcef8ff39"}, {"parameters": {"resource": "mailbox", "mailboxId": "={{$node[\"HelpScout9\"].json[\"id\"]}}"}, "name": "HelpScout10", "type": "n8n-nodes-base.helpScout", "typeVersion": 1, "position": [600, 150], "credentials": {"helpScoutOAuth2Api": {"id": "163", "name": "HelpScout OAuth2 API creds"}}, "id": "89e6ba09-db18-444c-99da-91bf95d2353b"}, {"parameters": {"resource": "thread", "conversationId": "={{$node[\"HelpScout5\"].json[\"id\"]}}", "type": "chat", "text": "=Thread_{{(new Date).toISOString()}}", "additionalFields": {"customerId": "={{$node[\"HelpScout\"].json[\"id\"]}}"}}, "name": "HelpScout11", "type": "n8n-nodes-base.helpScout", "typeVersion": 1, "position": [750, 600], "credentials": {"helpScoutOAuth2Api": {"id": "163", "name": "HelpScout OAuth2 API creds"}}, "id": "6bc6666d-dda1-485b-97de-a4b08407f22f"}, {"parameters": {"resource": "thread", "operation": "getAll", "conversationId": "={{$node[\"HelpScout5\"].json[\"id\"]}}", "limit": 1}, "name": "HelpScout12", "type": "n8n-nodes-base.helpScout", "typeVersion": 1, "position": [900, 600], "credentials": {"helpScoutOAuth2Api": {"id": "163", "name": "HelpScout OAuth2 API creds"}}, "id": "740d4be3-b1b7-4383-a2ce-716270774df7"}], "connections": {"HelpScout": {"main": [[{"node": "HelpScout5", "type": "main", "index": 0}]]}, "HelpScout1": {"main": [[{"node": "HelpScout2", "type": "main", "index": 0}]]}, "HelpScout2": {"main": [[{"node": "HelpScout3", "type": "main", "index": 0}]]}, "HelpScout3": {"main": [[{"node": "HelpScout4", "type": "main", "index": 0}]]}, "HelpScout5": {"main": [[{"node": "HelpScout11", "type": "main", "index": 0}]]}, "HelpScout6": {"main": [[{"node": "HelpScout7", "type": "main", "index": 0}]]}, "HelpScout7": {"main": [[{"node": "HelpScout8", "type": "main", "index": 0}]]}, "HelpScout8": {"main": [[{"node": "HelpScout1", "type": "main", "index": 0}]]}, "HelpScout9": {"main": [[{"node": "HelpScout10", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "HelpScout", "type": "main", "index": 0}, {"node": "HelpScout9", "type": "main", "index": 0}]]}, "HelpScout11": {"main": [[{"node": "HelpScout12", "type": "main", "index": 0}]]}, "HelpScout12": {"main": [[{"node": "HelpScout6", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}