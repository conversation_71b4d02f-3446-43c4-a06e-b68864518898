{"data": {"startData": {}, "resultData": {"runData": {"Start": [{"startTime": 1676891385640, "executionTime": 1, "source": [], "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "Hacker News": [{"startTime": 1676891385642, "executionTime": 700, "source": [{"previousNode": "Start"}], "executionStatus": "success", "data": {"main": [[{"json": {"created_at": "2018-03-14T03:50:30.000Z", "title": "<PERSON> has died", "url": "http://www.bbc.com/news/uk-43396008", "author": "Cogito", "points": 6015, "story_text": {"object": true}, "comment_text": {"object": true}, "num_comments": 436, "story_id": {"object": true}, "story_title": {"object": true}, "story_url": {"object": true}, "parent_id": {"object": true}, "created_at_i": 1520999430, "relevancy_score": 8012, "_tags": ["json array"], "objectID": "16582136", "_highlightResult": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "Hacker News1": [{"startTime": 1676891386343, "executionTime": 3784, "source": [{"previousNode": "Hacker News"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": 16582136, "created_at": "2018-03-14T03:50:30.000Z", "created_at_i": 1520999430, "type": "story", "author": "Cogito", "title": "<PERSON> has died", "url": "http://www.bbc.com/news/uk-43396008", "text": {"object": true}, "points": 6015, "parent_id": {"object": true}, "story_id": {"object": true}, "options": ["json array"]}, "pairedItem": {"item": 0}}]]}}], "Hacker News2": [{"startTime": 1676891390129, "executionTime": 598, "source": [{"previousNode": "Hacker News1"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": 119207, "username": "Cogito", "about": "You can get me at andrew.ardill at gmail.", "karma": 4263, "created_at": "2012-04-24T06:32:57.000Z", "avg": 3.52273, "delay": {"object": true}, "submitted": 745, "updated_at": "2022-12-06T03:45:32.000Z", "submission_count": 24, "comment_count": 720, "created_at_i": 1335249177, "objectID": "Cogito"}, "pairedItem": {"item": 0}}]]}}]}, "lastNodeExecuted": "Hacker News2"}, "executionData": {"contextData": {}, "nodeExecutionStack": [], "waitingExecution": {}, "waitingExecutionSource": {}}}, "mode": "cli", "startedAt": "2023-02-20T11:09:45.637Z", "stoppedAt": "2023-02-20T11:09:50.728Z", "status": "running", "finished": true}