{"createdAt": "2021-04-19T15:04:14.654Z", "updatedAt": "2021-04-20T08:10:42.292Z", "id": "173", "name": "GetResponse:Contact:create getAll get update delete", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300], "id": "5651ff0c-3724-4194-8070-66f3737a0762"}, {"parameters": {"operation": "create", "email": "=fake{{Date.now()}}@email.com", "campaignId": "M<PERSON>jt", "additionalFields": {"name": "=Name{{Date.now()}}"}}, "name": "GetResponse", "type": "n8n-nodes-base.getResponse", "typeVersion": 1, "position": [500, 300], "credentials": {"getResponseApi": {"id": "143", "name": "GetResponse API creds"}}, "id": "a3fa00e2-6539-4066-ac49-a6e2561397a9"}, {"parameters": {"operation": "getAll", "limit": 1, "options": {"sortBy": "createdOn", "sortOrder": "DESC"}}, "name": "GetResponse1", "type": "n8n-nodes-base.getResponse", "typeVersion": 1, "position": [900, 300], "credentials": {"getResponseApi": {"id": "143", "name": "GetResponse API creds"}}, "id": "6abe5d00-d52a-4624-a45b-e7508bdc8cbc"}, {"parameters": {"contactId": "={{$node[\"GetResponse1\"].json[\"contactId\"]}}", "options": {}}, "name": "GetResponse2", "type": "n8n-nodes-base.getResponse", "typeVersion": 1, "position": [1100, 300], "credentials": {"getResponseApi": {"id": "143", "name": "GetResponse API creds"}}, "id": "f6ac6160-9cf1-4f5f-88e9-6c1fee5328bd"}, {"parameters": {"operation": "update", "contactId": "={{$node[\"GetResponse1\"].json[\"contactId\"]}}", "updateFields": {"email": "=Updated{{$node[\"GetResponse1\"].json[\"email\"]}}", "name": "=Updated{{$node[\"GetResponse1\"].json[\"name\"]}}"}}, "name": "GetResponse3", "type": "n8n-nodes-base.getResponse", "typeVersion": 1, "position": [1300, 300], "credentials": {"getResponseApi": {"id": "143", "name": "GetResponse API creds"}}, "id": "af223c10-9e82-4e4f-b4a2-fbcb87e3a81f"}, {"parameters": {"operation": "delete", "contactId": "={{$node[\"GetResponse1\"].json[\"contactId\"]}}", "options": {}}, "name": "GetResponse4", "type": "n8n-nodes-base.getResponse", "typeVersion": 1, "position": [1500, 300], "credentials": {"getResponseApi": {"id": "143", "name": "GetResponse API creds"}}, "id": "85f302a7-b522-4e38-a88a-07e0705f0711"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(1000);\n\n// Output data\nreturn items;"}, "name": "Sleep 1 second", "type": "n8n-nodes-base.function", "position": [700, 300], "typeVersion": 1, "id": "994c0e7a-3a6d-4cdd-8fa6-f62d67f15a70"}], "connections": {"GetResponse1": {"main": [[{"node": "GetResponse2", "type": "main", "index": 0}]]}, "GetResponse2": {"main": [[{"node": "GetResponse3", "type": "main", "index": 0}]]}, "GetResponse3": {"main": [[{"node": "GetResponse4", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "GetResponse", "type": "main", "index": 0}]]}, "Sleep 1 second": {"main": [[{"node": "GetResponse1", "type": "main", "index": 0}]]}, "GetResponse": {"main": [[{"node": "Sleep 1 second", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}