{"createdAt": "2021-02-22T13:24:15.920Z", "updatedAt": "2021-07-14T13:33:54.633Z", "id": "57", "name": "Mindee:receipt:predict:invoice:predict", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [330, 230], "id": "a2fb028a-a712-440d-b273-88e18c86f688"}, {"parameters": {}, "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.mindee", "typeVersion": 1, "position": [650, 150], "credentials": {"mindeeReceiptApi": {"id": "44", "name": "Mindee Receipt creds"}}, "id": "d1c7c807-0f46-48d7-a6a8-07c6fbd9298e"}, {"parameters": {"url": "https://upload.wikimedia.org/wikipedia/commons/0/0b/ReceiptSwiss.jpg", "responseFormat": "file", "options": {}, "headerParametersUi": {"parameter": [{"name": "User-agent", "value": "n8n-io app"}]}}, "name": "HTTP Request", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [500, 150], "id": "8313ed3a-96d5-4f34-8d5c-76bcdc7be95a"}, {"parameters": {"resource": "invoice"}, "name": "Mindee1", "type": "n8n-nodes-base.mindee", "typeVersion": 1, "position": [650, 300], "credentials": {"mindeeInvoiceApi": {"id": "45", "name": "Mindee Invoice creds"}}, "id": "1632bf97-ed0a-49cc-8c55-be7be333c91b"}, {"parameters": {"url": "https://www.invoicesimple.com/wp-content/uploads/2018/06/Sample-Invoice-printable.png", "responseFormat": "file", "options": {}}, "name": "HTTP Request1", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [500, 300], "id": "72d76ab1-1074-42ca-af84-cf8dc7eef32f"}], "connections": {"Start": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}, {"node": "HTTP Request1", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "HTTP Request1": {"main": [[{"node": "Mindee1", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}