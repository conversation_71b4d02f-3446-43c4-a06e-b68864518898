{"data": {"startData": {}, "resultData": {"runData": {"Start": [{"startTime": 1676891413308, "executionTime": 1, "source": [], "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "DeepL": [{"startTime": 1676891413309, "executionTime": 601, "source": [{"previousNode": "Start"}], "executionStatus": "success", "data": {"main": [[{"json": {"detected_source_language": "EN", "text": "n8n (ausgesprochen n-eight-n) hilf<PERSON> <PERSON><PERSON><PERSON>, jede <PERSON> mit einer API auf der Welt miteinander zu verbinden, um ihre Daten ohne eine einzige Zeile Code zu teilen und zu manipulieren. Es ist ein einfach zu bedienender, benutzerfreundlicher und hochgradig anpassbarer Dienst."}, "pairedItem": {"item": 0}}]]}}], "Function": [{"startTime": 1676891413911, "executionTime": 10, "source": [{"previousNode": "DeepL"}], "executionStatus": "success", "data": {"main": [[{"json": {"detected_source_language": "EN", "text": "n8n (ausgesprochen n-eight-n) hilf<PERSON> <PERSON><PERSON><PERSON>, jede <PERSON> mit einer API auf der Welt miteinander zu verbinden, um ihre Daten ohne eine einzige Zeile Code zu teilen und zu manipulieren. Es ist ein einfach zu bedienender, benutzerfreundlicher und hochgradig anpassbarer Dienst."}, "pairedItem": {"item": 0}, "index": 0}]]}}]}, "lastNodeExecuted": "Function"}, "executionData": {"contextData": {}, "nodeExecutionStack": [], "waitingExecution": {}, "waitingExecutionSource": {}}}, "mode": "cli", "startedAt": "2023-02-20T11:10:13.307Z", "stoppedAt": "2023-02-20T11:10:13.921Z", "status": "running", "finished": true}