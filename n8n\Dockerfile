# 使用 PostgreSQL 作为基础镜像
FROM nikolaik/python-nodejs:python3.12-nodejs20

# 添加维护者信息
LABEL maintainer="ai来事 <https://www.youtube.com/@all.ai.>"

ARG CACHEBUST=222

# 设置构建参数，提供默认值
ARG WEBHOOK_URL=https://你的hf账户名-空间名.hf.space/
ARG POSTGRESDB_PORT=5432

# 设置基本环境变量
ENV WEBHOOK_URL=${WEBHOOK_URL} \
    N8N_HOST=0.0.0.0 \
    N8N_PORT=7860 \
    N8N_PROTOCOL=https \
    N8N_ENTERPRISE_MOCK=true \
    N8N_DEFAULT_LOCALE=zh-CN \
    GENERIC_TIMEZONE=Asia/Shanghai \
    N8N_METRICS=true \
    QUEUE_HEALTH_CHECK_ACTIVE=true \
    N8N_PAYLOAD_SIZE_MAX=256 \
    DB_TYPE=postgresdb \
    DB_POSTGRESDB_PORT=${POSTGRESDB_PORT} \
    VIRTUAL_ENV=/home/<USER>/venv \
    PATH="/home/<USER>/venv/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:$PATH" \
    # 添加 Qdrant 配置
    QDRANT_HOST=http://localhost \
    QDRANT_PORT=6333 \
    QDRANT_VERSION=1.12.4 \
    # 添加超时配置
    WAIT_TIMEOUT=30 \
    # 添加日志级别
    N8N_LOG_LEVEL=info

# 安装系统依赖
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    unzip \
    gnupg \
    build-essential \
    sudo \
    vim \
    git \
    procps \
    lsof \
    net-tools \
    ca-certificates \
    openssl \
    tzdata \
    htop \
    jq \
    netcat-openbsd \
    redis-server \
    && ln -fs /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && dpkg-reconfigure --frontend noninteractive tzdata \
    # 下载并安装预编译的 Qdrant
    && cd /tmp \
    && curl -L https://github.com/qdrant/qdrant/releases/download/v${QDRANT_VERSION}/qdrant-x86_64-unknown-linux-gnu.tar.gz -o qdrant.tar.gz \
    && tar xvf qdrant.tar.gz \
    && mv qdrant /usr/local/bin/ \
    && rm qdrant.tar.gz \
    # 创建 Qdrant 配置目录和数据目录
    && mkdir -p /home/<USER>/.n8n/qdrant/storage \
    && mkdir -p /home/<USER>/.n8n/qdrant/config \
    && mkdir -p /home/<USER>/.n8n/qdrant/snapshots \
    && chown -R pn:pn /home/<USER>/.n8n/qdrant \
    && chmod -R 755 /home/<USER>/.n8n/qdrant \
    # 清理
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/* \
    # 创建虚拟环境并安装 Python 依赖
    && python3 -m venv $VIRTUAL_ENV \
    && $VIRTUAL_ENV/bin/pip install --no-cache-dir --upgrade pip requests yt-dlp \
    # 安装 n8n
    && npm install -g n8n@latest pnpm \
    && npm cache clean --force

# 创建环境变量文件
RUN --mount=type=secret,id=POSTGRES_USER,mode=0444,required=true \
    --mount=type=secret,id=POSTGRES_PASSWORD,mode=0444,required=true \
    --mount=type=secret,id=POSTGRES_DB,mode=0444,required=true \
    --mount=type=secret,id=POSTGRESDB_HOST,mode=0444,required=true \
    echo "export DB_POSTGRESDB_HOST=$(cat /run/secrets/POSTGRESDB_HOST)" >> /home/<USER>/.env && \
    echo "export DB_POSTGRESDB_USER=$(cat /run/secrets/POSTGRES_USER)" >> /home/<USER>/.env && \
    echo "export DB_POSTGRESDB_PASSWORD=$(cat /run/secrets/POSTGRES_PASSWORD)" >> /home/<USER>/.env && \
    echo "export DB_POSTGRESDB_DATABASE=$(cat /run/secrets/POSTGRES_DB)" >> /home/<USER>/.env && \
    chown pn:pn /home/<USER>/.env

# 创建工作目录
WORKDIR /home/<USER>/n8n

# 复制启动脚本并创建数据目录
COPY --chown=pn:pn run.sh ./run.sh
COPY --chown=pn:pn config/n8n_env.sh ./config/n8n_env.sh
RUN chmod +x ./run.sh \
    && mkdir -p /home/<USER>/.n8n \
    && chown -R pn:pn /home/<USER>/.n8n \
    && chown -R pn:pn /home/<USER>/n8n

# 暴露端口
EXPOSE 7860

# 切换到非 root 用户
USER pn

# 设置数据卷
VOLUME ["/home/<USER>/.n8n"]

# 启动命令
CMD ["./run.sh"]