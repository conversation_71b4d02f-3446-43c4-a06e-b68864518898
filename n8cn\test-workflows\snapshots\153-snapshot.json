{"data": {"startData": {}, "resultData": {"runData": {"Start": [{"startTime": 1747343994432, "executionIndex": 0, "source": [], "hints": [], "executionTime": 1, "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "AWS Lambda": [{"startTime": 1747343994433, "executionIndex": 1, "source": [{"previousNode": "Start"}], "hints": [], "executionTime": 493, "executionStatus": "success", "data": {"main": [[{"json": {"result": "Hello world!, this is n8n"}, "pairedItem": {"item": 0}}]]}}], "AWS Lambda1": [{"startTime": 1747343994926, "executionIndex": 2, "source": [{"previousNode": "Start"}], "hints": [], "executionTime": 296, "executionStatus": "success", "data": {"main": [[{"json": {"result": "Hello world!, this is nodeqa"}, "pairedItem": {"item": 0}}]]}}]}, "lastNodeExecuted": "AWS Lambda1"}, "executionData": {"contextData": {}, "nodeExecutionStack": [], "metadata": {}, "waitingExecution": {}, "waitingExecutionSource": {}}}, "mode": "cli", "startedAt": "2025-05-15T21:19:54.431Z", "stoppedAt": "2025-05-15T21:19:55.222Z", "status": "running", "finished": true}