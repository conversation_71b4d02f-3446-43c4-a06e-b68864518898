{"createdAt": "2021-02-23T15:27:09.090Z", "updatedAt": "2021-07-16T07:21:35.499Z", "id": "60", "name": "Taiga:Issue:create update get getAll delete", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [170, 350], "id": "f7f8b0e2-b017-4579-b9e0-3ba533335d3a"}, {"parameters": {"projectId": 399742, "subject": "Test", "additionalFields": {"description": "TestIssue"}}, "name": "Taiga", "type": "n8n-nodes-base.taiga", "typeVersion": 1, "position": [350, 350], "credentials": {"taigaApi": {"id": "221", "name": "taigaApi"}}, "id": "c7b5324f-05d8-4eb7-a4f2-0f0a4c6de1da"}, {"parameters": {"operation": "update", "projectId": 399742, "issueId": "={{$node[\"Taiga\"].json[\"id\"]}}", "updateFields": {"subject": "UpdatedTest"}}, "name": "Taiga1", "type": "n8n-nodes-base.taiga", "typeVersion": 1, "position": [500, 350], "credentials": {"taigaApi": {"id": "221", "name": "taigaApi"}}, "id": "c156fd69-32cb-4b38-b85a-aeac05032c23"}, {"parameters": {"operation": "get", "issueId": "={{$node[\"Taiga\"].json[\"id\"]}}"}, "name": "Taiga2", "type": "n8n-nodes-base.taiga", "typeVersion": 1, "position": [650, 350], "credentials": {"taigaApi": {"id": "221", "name": "taigaApi"}}, "id": "2f3418a5-b09a-4db3-9cb8-c8d10b31a4b6"}, {"parameters": {"operation": "getAll", "projectId": 399742, "limit": 1, "filters": {}}, "name": "Taiga3", "type": "n8n-nodes-base.taiga", "typeVersion": 1, "position": [800, 350], "credentials": {"taigaApi": {"id": "221", "name": "taigaApi"}}, "id": "a100f3a0-d4ae-41f3-81d5-21101a91eef6"}, {"parameters": {"operation": "delete", "issueId": "={{$node[\"Taiga\"].json[\"id\"]}}"}, "name": "Taiga4", "type": "n8n-nodes-base.taiga", "typeVersion": 1, "position": [950, 350], "credentials": {"taigaApi": {"id": "221", "name": "taigaApi"}}, "id": "a1c664cd-c0f0-44af-94ce-6c650aee489c"}], "connections": {"Start": {"main": [[{"node": "Taiga", "type": "main", "index": 0}]]}, "Taiga": {"main": [[{"node": "Taiga1", "type": "main", "index": 0}]]}, "Taiga1": {"main": [[{"node": "Taiga2", "type": "main", "index": 0}]]}, "Taiga2": {"main": [[{"node": "Taiga3", "type": "main", "index": 0}]]}, "Taiga3": {"main": [[{"node": "Taiga4", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}