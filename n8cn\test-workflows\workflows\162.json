{"createdAt": "2021-04-06T08:27:12.354Z", "updatedAt": "2021-04-19T08:16:20.657Z", "id": "162", "name": "AWSSQS:sendMessage", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300], "id": "61c9e1a4-21ab-4146-82ae-4c4388c1da30"}, {"parameters": {"queue": "https://sqs.us-east-1.amazonaws.com/589875339869/StandardTestQueue", "sendInputData": false, "message": "=StandardMessage{{Date.now()}}", "options": {"messageAttributes": {"string": [{"name": "type", "value": "test"}]}}}, "name": "AWS SQS", "type": "n8n-nodes-base.awsSqs", "typeVersion": 1, "position": [550, 200], "credentials": {"aws": {"id": "124", "name": "AWS creds"}}, "id": "0194b047-c629-4f68-a629-12455de6ecf6"}, {"parameters": {"queue": "https://sqs.us-east-1.amazonaws.com/589875339869/FifoTestQueue.fifo", "queueType": "fifo", "sendInputData": false, "message": "=FIFOMessage{{Date.now()}}", "messageGroupId": "TestMessageGroupId", "options": {"messageAttributes": {"string": [{"name": "type", "value": "test"}]}, "messageDeduplicationId": "DedupTestMessageGroupId"}}, "name": "AWS SQS1", "type": "n8n-nodes-base.awsSqs", "typeVersion": 1, "position": [550, 380], "credentials": {"aws": {"id": "124", "name": "AWS creds"}}, "id": "25bc7b70-06c8-4605-8744-f27ab8765761"}], "connections": {"Start": {"main": [[{"node": "AWS SQS1", "type": "main", "index": 0}, {"node": "AWS SQS", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}