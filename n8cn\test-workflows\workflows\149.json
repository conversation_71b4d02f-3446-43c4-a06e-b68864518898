{"createdAt": "2021-03-24T10:13:37.863Z", "updatedAt": "2021-07-29T16:13:27.056Z", "id": "149", "name": "Reddit:User:get:Subreddit:get getAll:Profile:get:Post:create get getAll search delete:PostComment:create getAll reply delete", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300], "id": "0591985b-5e87-405c-9836-f3445e04edf6"}, {"parameters": {"resource": "user", "username": "nodeqa"}, "name": "Reddit", "type": "n8n-nodes-base.reddit", "typeVersion": 1, "position": [450, 150], "id": "56e5318b-d8b6-4e9b-a847-29871a1671a1"}, {"parameters": {"resource": "user", "username": "nodeqa", "details": "comments", "limit": 1}, "name": "Reddit1", "type": "n8n-nodes-base.reddit", "typeVersion": 1, "position": [600, 150], "alwaysOutputData": true, "id": "31923945-dd5e-4fc2-8f6d-2aa45e32b06b"}, {"parameters": {"resource": "user", "username": "nodeqa", "details": "gilded", "limit": 1}, "name": "Reddit2", "type": "n8n-nodes-base.reddit", "typeVersion": 1, "position": [750, 150], "alwaysOutputData": true, "id": "a7954654-dc0e-44eb-9ff3-f9b5a8f2b1a0"}, {"parameters": {"resource": "user", "username": "nodeqa", "details": "overview", "limit": 1}, "name": "Reddit3", "type": "n8n-nodes-base.reddit", "typeVersion": 1, "position": [900, 150], "alwaysOutputData": true, "id": "175fcb7c-a5ee-4900-aeee-4a4bfe2ad1be"}, {"parameters": {"resource": "user", "username": "nodeqa", "details": "submitted", "limit": 1}, "name": "Reddit4", "type": "n8n-nodes-base.reddit", "typeVersion": 1, "position": [1050, 150], "alwaysOutputData": true, "id": "6d3148bb-7c31-435c-aad2-06272d3a8197"}, {"parameters": {"resource": "subreddit", "subreddit": "nocode"}, "name": "Reddit5", "type": "n8n-nodes-base.reddit", "typeVersion": 1, "position": [450, 300], "alwaysOutputData": true, "id": "33965804-57f3-4f25-afb8-68715fa81354"}, {"parameters": {"resource": "subreddit", "operation": "getAll", "limit": 1, "filters": {"trending": false}}, "name": "Reddit6", "type": "n8n-nodes-base.reddit", "typeVersion": 1, "position": [600, 300], "alwaysOutputData": true, "id": "f918e479-b30a-4fa0-8830-89dd7edf5004"}, {"parameters": {"resource": "profile"}, "name": "Reddit7", "type": "n8n-nodes-base.reddit", "typeVersion": 1, "position": [450, 450], "alwaysOutputData": true, "credentials": {"redditOAuth2Api": {"id": "120", "name": "Reddit OAuth2 API creds"}}, "notes": "IGNORED_PROPERTIES=cookie_consent_banner", "id": "5747c453-08f3-4777-8cee-36315d2dfdc0"}, {"parameters": {"resource": "profile", "details": "prefs"}, "name": "Reddit8", "type": "n8n-nodes-base.reddit", "typeVersion": 1, "position": [600, 450], "alwaysOutputData": true, "credentials": {"redditOAuth2Api": {"id": "120", "name": "Reddit OAuth2 API creds"}}, "id": "7301f84d-f865-4ce6-b754-26c6688927dc"}, {"parameters": {"resource": "profile", "details": "trophies"}, "name": "Reddit9", "type": "n8n-nodes-base.reddit", "typeVersion": 1, "position": [750, 450], "alwaysOutputData": true, "credentials": {"redditOAuth2Api": {"id": "120", "name": "Reddit OAuth2 API creds"}}, "id": "6e6261e9-4b73-4cfd-9c43-06e3419204e1"}, {"parameters": {"subreddit": "test", "title": "=Title{{Date.now()}}", "text": "=Test post {{Date.now()}}"}, "name": "Reddit10", "type": "n8n-nodes-base.reddit", "typeVersion": 1, "position": [450, 600], "alwaysOutputData": true, "credentials": {"redditOAuth2Api": {"id": "120", "name": "Reddit OAuth2 API creds"}}, "id": "008c54e0-80a0-478d-a71e-3d84ede2952e"}, {"parameters": {"operation": "get", "subreddit": "test", "postId": "={{$node[\"Reddit10\"].json[\"id\"]}}"}, "name": "Reddit11", "type": "n8n-nodes-base.reddit", "typeVersion": 1, "position": [600, 600], "alwaysOutputData": true, "credentials": {"redditOAuth2Api": {"id": "120", "name": "Reddit OAuth2 API creds"}}, "id": "57990704-189f-4f6a-8fc5-6bb9c6593f05"}, {"parameters": {"operation": "getAll", "subreddit": "test", "limit": 1, "filters": {"category": "new"}}, "name": "Reddit12", "type": "n8n-nodes-base.reddit", "typeVersion": 1, "position": [750, 600], "alwaysOutputData": true, "credentials": {"redditOAuth2Api": {"id": "120", "name": "Reddit OAuth2 API creds"}}, "id": "bbb708d3-a7b5-42fe-bc28-e617f0a95dff"}, {"parameters": {"operation": "search", "subreddit": "test", "keyword": "post", "limit": 1, "additionalFields": {"sort": "new"}}, "name": "Reddit13", "type": "n8n-nodes-base.reddit", "typeVersion": 1, "position": [900, 600], "alwaysOutputData": true, "credentials": {"redditOAuth2Api": {"id": "120", "name": "Reddit OAuth2 API creds"}}, "notes": "IGNORED_PROPERTIES=post_hint,preview,url_overridden_by_dest,is_gallery,gallery_data,media_metadata", "id": "2201c9ff-a721-4b00-a17f-d285bd9daee7"}, {"parameters": {"operation": "delete", "postId": "={{$node[\"Reddit10\"].json[\"id\"]}}"}, "name": "Reddit14", "type": "n8n-nodes-base.reddit", "typeVersion": 1, "position": [1960, 600], "alwaysOutputData": true, "credentials": {"redditOAuth2Api": {"id": "120", "name": "Reddit OAuth2 API creds"}}, "id": "6653bae4-be12-47a1-b515-a88ca01a0f03"}, {"parameters": {"resource": "postComment", "postId": "={{$node[\"Reddit10\"].json[\"id\"]}}", "commentText": "=Comment{{Date.now()}}"}, "name": "Reddit15", "type": "n8n-nodes-base.reddit", "typeVersion": 1, "position": [1050, 750], "alwaysOutputData": true, "credentials": {"redditOAuth2Api": {"id": "120", "name": "Reddit OAuth2 API creds"}}, "id": "d11b902b-9839-436b-b370-e90d61b9ed80"}, {"parameters": {"resource": "postComment", "operation": "getAll", "subreddit": "test", "postId": "={{$node[\"Reddit10\"].json[\"id\"]}}", "limit": 1}, "name": "Reddit16", "type": "n8n-nodes-base.reddit", "typeVersion": 1, "position": [1360, 750], "alwaysOutputData": true, "credentials": {"redditOAuth2Api": {"id": "120", "name": "Reddit OAuth2 API creds"}}, "id": "a90dfeb6-1940-41eb-8083-eb9e7003bf10"}, {"parameters": {"resource": "postComment", "operation": "reply", "commentId": "={{$node[\"Reddit15\"].json[\"id\"]}}", "replyText": "=ReplyComment{{Date.now()}}"}, "name": "Reddit17", "type": "n8n-nodes-base.reddit", "typeVersion": 1, "position": [1510, 750], "alwaysOutputData": true, "credentials": {"redditOAuth2Api": {"id": "120", "name": "Reddit OAuth2 API creds"}}, "id": "c80efe81-fc7d-4523-ba4c-11e0e22e064a"}, {"parameters": {"resource": "postComment", "operation": "delete", "commentId": "={{$node[\"Reddit15\"].json[\"id\"]}}"}, "name": "Reddit18", "type": "n8n-nodes-base.reddit", "typeVersion": 1, "position": [1660, 750], "alwaysOutputData": true, "credentials": {"redditOAuth2Api": {"id": "120", "name": "Reddit OAuth2 API creds"}}, "id": "ad24a5e6-0d22-441e-a7f9-d74dcf49ecd4"}, {"parameters": {"resource": "postComment", "operation": "delete", "commentId": "={{$node[\"Reddit17\"].json[\"id\"]}}"}, "name": "Reddit19", "type": "n8n-nodes-base.reddit", "typeVersion": 1, "position": [1810, 750], "alwaysOutputData": true, "credentials": {"redditOAuth2Api": {"id": "120", "name": "Reddit OAuth2 API creds"}}, "id": "a9ec2a18-187e-47ee-a5ce-73bbd02576f2"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(10000);\n\n// Output data\nreturn items;"}, "name": "Sleep 10 seconds", "type": "n8n-nodes-base.function", "position": [1200, 750], "typeVersion": 1, "id": "858b86f3-9efd-45b0-88e7-c2b25d560159"}], "connections": {"Reddit": {"main": [[{"node": "Reddit1", "type": "main", "index": 0}]]}, "Reddit1": {"main": [[{"node": "Reddit2", "type": "main", "index": 0}]]}, "Reddit2": {"main": [[{"node": "Reddit3", "type": "main", "index": 0}]]}, "Reddit3": {"main": [[{"node": "Reddit4", "type": "main", "index": 0}]]}, "Reddit5": {"main": [[{"node": "Reddit6", "type": "main", "index": 0}]]}, "Reddit7": {"main": [[{"node": "Reddit8", "type": "main", "index": 0}]]}, "Reddit8": {"main": [[{"node": "Reddit9", "type": "main", "index": 0}]]}, "Reddit10": {"main": [[{"node": "Reddit11", "type": "main", "index": 0}]]}, "Reddit11": {"main": [[{"node": "Reddit12", "type": "main", "index": 0}]]}, "Reddit12": {"main": [[{"node": "Reddit13", "type": "main", "index": 0}]]}, "Reddit13": {"main": [[{"node": "Reddit15", "type": "main", "index": 0}]]}, "Reddit15": {"main": [[{"node": "Sleep 10 seconds", "type": "main", "index": 0}]]}, "Reddit16": {"main": [[{"node": "Reddit17", "type": "main", "index": 0}]]}, "Reddit17": {"main": [[{"node": "Reddit18", "type": "main", "index": 0}]]}, "Reddit18": {"main": [[{"node": "Reddit19", "type": "main", "index": 0}]]}, "Reddit19": {"main": [[{"node": "Reddit14", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "Reddit", "type": "main", "index": 0}, {"node": "Reddit5", "type": "main", "index": 0}, {"node": "Reddit7", "type": "main", "index": 0}, {"node": "Reddit10", "type": "main", "index": 0}]]}, "Sleep 10 seconds": {"main": [[{"node": "Reddit16", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}