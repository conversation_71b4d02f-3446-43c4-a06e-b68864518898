# N8N 企业版 Hugging Face 部署指南

## 🔧 最新更新 (2025-07-19)

**修复了构建问题：**
- ✅ 修复了 Alpine Linux 兼容性问题
- ✅ 简化了依赖安装，提高构建成功率
- ✅ 优化了启动脚本，减少潜在错误
- ✅ 移除了可能导致冲突的 Redis/Qdrant 配置

## 📋 部署前准备

### 1. 数据库准备

您需要一个外部 PostgreSQL 数据库。推荐的免费数据库服务：

- **Supabase**: https://supabase.com (推荐)
- **Neon**: https://neon.tech
- **Railway**: https://railway.app
- **PlanetScale**: https://planetscale.com (MySQL)

### 2. 获取数据库连接信息

从您的数据库服务商获取以下信息：
- 主机地址 (Host)
- 端口 (Port, 通常是 5432)
- 数据库名称 (Database)
- 用户名 (Username)
- 密码 (Password)

## 🚀 Hugging Face 部署步骤

### 步骤 1: 创建新的 Space

1. 访问 [Hugging Face Spaces](https://huggingface.co/spaces)
2. 点击 "Create new Space"
3. 填写以下信息：
   - **Space name**: `n8n-enterprise` (或您喜欢的名称)
   - **License**: `apache-2.0`
   - **SDK**: `Docker`
   - **Hardware**: `CPU basic` (免费) 或 `CPU upgrade` (付费，性能更好)

### 步骤 2: 上传项目文件

将以下文件上传到您的 Space：

```
n8n-enterprise/
├── Dockerfile
├── README.md
├── run.sh
└── config/
    └── n8n_env.sh
```

### 步骤 3: 配置环境变量

在 Space 的 Settings 页面，添加以下环境变量：

#### 必需的数据库配置
```bash
POSTGRES_USER=your_postgres_username
POSTGRES_PASSWORD=your_postgres_password
POSTGRES_DB=your_database_name
POSTGRESDB_HOST=your_postgres_host
```

#### 可选配置
```bash
# 自定义 Webhook URL (可选，系统会自动设置)
WEBHOOK_URL=https://your-username-space-name.hf.space/

# 数据库端口 (默认 5432)
POSTGRESDB_PORT=5432
```

### 步骤 4: 启动部署

1. 保存环境变量配置
2. Space 会自动开始构建和部署
3. 等待构建完成（通常需要 5-10 分钟）

### 步骤 5: 访问应用

1. 构建完成后，点击 Space 页面上的应用链接
2. 首次访问会要求创建管理员账户
3. 完成设置后即可开始使用

## 🔧 配置示例

### Supabase 数据库配置示例

如果您使用 Supabase，环境变量配置如下：

```bash
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_supabase_password
POSTGRES_DB=postgres
POSTGRESDB_HOST=db.your-project-ref.supabase.co
POSTGRESDB_PORT=5432
```

### Neon 数据库配置示例

如果您使用 Neon，环境变量配置如下：

```bash
POSTGRES_USER=your_neon_username
POSTGRES_PASSWORD=your_neon_password
POSTGRES_DB=neondb
POSTGRESDB_HOST=ep-xxx-xxx.us-east-2.aws.neon.tech
POSTGRESDB_PORT=5432
```

## 📊 监控和维护

### 查看日志

在 Hugging Face Space 的 "Logs" 标签页中可以查看：
- 应用启动日志
- 数据库连接状态
- 服务运行状态
- 错误信息

### 重启应用

如果需要重启应用：
1. 进入 Space 的 Settings
2. 点击 "Restart this Space"
3. 等待重新部署完成

### 更新应用

要更新到新版本：
1. 替换相关文件
2. 系统会自动重新构建
3. 等待部署完成

## 🛠️ 故障排除

### 常见问题及解决方案

#### 1. 数据库连接失败

**错误信息**: `Database connection failed`

**解决方案**:
- 检查数据库环境变量是否正确
- 确认数据库服务正在运行
- 验证网络连接是否正常
- 检查数据库防火墙设置

#### 2. 应用启动超时

**错误信息**: `Application startup timeout`

**解决方案**:
- 升级到付费的 CPU 配置
- 检查数据库响应速度
- 查看详细日志信息

#### 3. 企业功能未启用

**现象**: 看不到企业版功能

**解决方案**:
- 确认环境变量 `N8N_ENTERPRISE_MOCK=true` 已设置
- 重启应用
- 清除浏览器缓存

#### 4. 中文界面未显示

**现象**: 界面仍为英文

**解决方案**:
- 确认 `N8N_DEFAULT_LOCALE=zh-CN` 已设置
- 在用户设置中手动切换语言
- 清除浏览器缓存

### 性能优化建议

1. **升级硬件**: 使用付费的 CPU upgrade 配置
2. **数据库优化**: 选择地理位置较近的数据库服务
3. **缓存配置**: Redis 缓存已自动配置
4. **监控资源**: 定期查看资源使用情况

## 📞 获取帮助

如果遇到问题，可以：

1. 查看 [N8N 官方文档](https://docs.n8n.io)
2. 访问 [N8N 社区论坛](https://community.n8n.io)
3. 查看 [企业版功能说明](https://github.com/deluxebear/n8n/blob/chs/ENTERPRISE-MOCK-README.md)
4. 在 GitHub 上提交 Issue

---

**祝您部署成功！** 🎉
