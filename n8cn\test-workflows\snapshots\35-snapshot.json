{"data": {"startData": {}, "resultData": {"runData": {"Start": [{"startTime": 1747344002190, "executionIndex": 0, "source": [], "hints": [], "executionTime": 0, "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "Slack": [{"startTime": 1747344002190, "executionIndex": 1, "source": [{"previousNode": "Start"}], "hints": [], "executionTime": 121, "executionStatus": "success", "data": {"main": [[{"json": {"title": "", "phone": "", "skype": "", "real_name": "nodeqa", "real_name_normalized": "nodeqa", "display_name": "", "display_name_normalized": "", "fields": {"object": true}, "status_text": "Testing...", "status_emoji": ":speech_balloon:", "status_emoji_display_info": ["json array"], "status_expiration": 0, "avatar_hash": "gc81277605b1", "email": "<EMAIL>", "image_24": "https://secure.gravatar.com/avatar/c81277605b129fdafaacede5ae34e07c.jpg?s=24&d=https%3A%2F%2Fa.slack-edge.com%2Fdf10d%2Fimg%2Favatars%2Fava_0015-24.png", "image_32": "https://secure.gravatar.com/avatar/c81277605b129fdafaacede5ae34e07c.jpg?s=32&d=https%3A%2F%2Fa.slack-edge.com%2Fdf10d%2Fimg%2Favatars%2Fava_0015-32.png", "image_48": "https://secure.gravatar.com/avatar/c81277605b129fdafaacede5ae34e07c.jpg?s=48&d=https%3A%2F%2Fa.slack-edge.com%2Fdf10d%2Fimg%2Favatars%2Fava_0015-48.png", "image_72": "https://secure.gravatar.com/avatar/c81277605b129fdafaacede5ae34e07c.jpg?s=72&d=https%3A%2F%2Fa.slack-edge.com%2Fdf10d%2Fimg%2Favatars%2Fava_0015-72.png", "image_192": "https://secure.gravatar.com/avatar/c81277605b129fdafaacede5ae34e07c.jpg?s=192&d=https%3A%2F%2Fa.slack-edge.com%2Fdf10d%2Fimg%2Favatars%2Fava_0015-192.png", "image_512": "https://secure.gravatar.com/avatar/c81277605b129fdafaacede5ae34e07c.jpg?s=512&d=https%3A%2F%2Fa.slack-edge.com%2Fdf10d%2Fimg%2Favatars%2Fava_0015-512.png", "status_text_canonical": ""}, "pairedItem": {"item": 0}}]]}}], "Slack13": [{"startTime": 1747344002311, "executionIndex": 2, "source": [{"previousNode": "Start"}], "hints": [], "executionTime": 253, "executionStatus": "success", "data": {"main": [[{"json": {"ok": true, "channel": "C01MZ82T9TR", "ts": "1747344002.517089", "message": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "Slack1": [{"startTime": 1747344002564, "executionIndex": 3, "source": [{"previousNode": "<PERSON><PERSON>ck"}], "hints": [], "executionTime": 250, "executionStatus": "success", "data": {"main": [[{"json": {"title": "", "phone": "", "skype": "", "real_name": "nodeqa", "real_name_normalized": "nodeqa", "display_name": "", "display_name_normalized": "", "fields": {"object": true}, "status_text": "Testing...", "status_emoji": ":speech_balloon:", "status_emoji_display_info": ["json array"], "status_expiration": 0, "avatar_hash": "gc81277605b1", "email": "<EMAIL>", "image_24": "https://secure.gravatar.com/avatar/c81277605b129fdafaacede5ae34e07c.jpg?s=24&d=https%3A%2F%2Fa.slack-edge.com%2Fdf10d%2Fimg%2Favatars%2Fava_0015-24.png", "image_32": "https://secure.gravatar.com/avatar/c81277605b129fdafaacede5ae34e07c.jpg?s=32&d=https%3A%2F%2Fa.slack-edge.com%2Fdf10d%2Fimg%2Favatars%2Fava_0015-32.png", "image_48": "https://secure.gravatar.com/avatar/c81277605b129fdafaacede5ae34e07c.jpg?s=48&d=https%3A%2F%2Fa.slack-edge.com%2Fdf10d%2Fimg%2Favatars%2Fava_0015-48.png", "image_72": "https://secure.gravatar.com/avatar/c81277605b129fdafaacede5ae34e07c.jpg?s=72&d=https%3A%2F%2Fa.slack-edge.com%2Fdf10d%2Fimg%2Favatars%2Fava_0015-72.png", "image_192": "https://secure.gravatar.com/avatar/c81277605b129fdafaacede5ae34e07c.jpg?s=192&d=https%3A%2F%2Fa.slack-edge.com%2Fdf10d%2Fimg%2Favatars%2Fava_0015-192.png", "image_512": "https://secure.gravatar.com/avatar/c81277605b129fdafaacede5ae34e07c.jpg?s=512&d=https%3A%2F%2Fa.slack-edge.com%2Fdf10d%2Fimg%2Favatars%2Fava_0015-512.png", "status_text_canonical": ""}, "pairedItem": {"item": 0}}]]}}], "Slack18": [{"startTime": 1747344002814, "executionIndex": 4, "source": [{"previousNode": "Slack13"}], "hints": [], "executionTime": 165, "executionStatus": "success", "data": {"main": [[{"json": {"ok": true}, "pairedItem": {"item": 0}}]]}}], "Slack24": [{"startTime": 1747344002979, "executionIndex": 5, "source": [{"previousNode": "Slack13"}], "hints": [], "executionTime": 138, "executionStatus": "success", "data": {"main": [[{"json": {"ok": true, "presence": "away", "online": false, "auto_away": false, "manual_away": false, "connection_count": 0}, "pairedItem": {"item": 0}}]]}}], "Slack19": [{"startTime": 1747344003117, "executionIndex": 6, "source": [{"previousNode": "Slack18"}], "hints": [], "executionTime": 223, "executionStatus": "success", "data": {"main": [[{"json": {"ok": true, "type": "message", "message": {"object": true}, "channel": "C01MZ82T9TR"}, "pairedItem": {"item": 0}}]]}}], "Slack25": [{"startTime": 1747344003340, "executionIndex": 7, "source": [{"previousNode": "Slack24"}], "hints": [], "executionTime": 189, "executionStatus": "success", "data": {"main": [[{"json": {"id": "U01NE1E8FFU", "team_id": "T01MZ82R8NB", "name": "nodeqa", "deleted": false, "color": "9f69e7", "real_name": "nodeqa", "tz": "Europe/Brussels", "tz_label": "Central European Summer Time", "tz_offset": 7200, "profile": {"object": true}, "is_admin": true, "is_owner": true, "is_primary_owner": true, "is_restricted": false, "is_ultra_restricted": false, "is_bot": false, "is_app_user": false, "updated": 1747344002, "is_email_confirmed": true, "has_2fa": false, "who_can_share_contact_card": "EVERYONE"}, "pairedItem": {"item": 0}}]]}}], "Slack20": [{"startTime": 1747344003529, "executionIndex": 8, "source": [{"previousNode": "Slack19"}], "hints": [], "executionTime": 152, "executionStatus": "success", "data": {"main": [[{"json": {"ok": true}, "pairedItem": {"item": 0}}]]}}], "Slack14": [{"startTime": 1747344003681, "executionIndex": 9, "source": [{"previousNode": "Slack20"}], "hints": [], "executionTime": 214, "executionStatus": "success", "data": {"main": [[{"json": {"ok": true, "permalink": "https://n8n-qa.slack.com/archives/C01MZ82T9TR/p1747344002517089", "channel": "C01MZ82T9TR"}, "pairedItem": {"item": 0}}]]}}], "Slack15": [{"startTime": 1747344003895, "executionIndex": 10, "source": [{"previousNode": "Slack14"}], "hints": [], "executionTime": 233, "executionStatus": "success", "data": {"main": [[{"json": {"ok": true, "channel": "C01MZ82T9TR", "ts": "1747344002.517089", "text": "Message Updated ", "message": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "Slack16": [{"startTime": 1747344004128, "executionIndex": 11, "source": [{"previousNode": "Slack15"}], "hints": [], "executionTime": 222, "executionStatus": "success", "data": {"main": [[{"json": {"ok": true, "message_ts": "1747344004.002900"}, "pairedItem": {"item": 0}}]]}}], "Slack17": [{"startTime": 1747344004351, "executionIndex": 12, "source": [{"previousNode": "Slack16"}], "hints": [], "executionTime": 208, "executionStatus": "success", "data": {"main": [[{"json": {"ok": true, "channel": "C01MZ82T9TR", "ts": "1747344002.517089"}, "pairedItem": {"item": 0}}]]}}]}, "lastNodeExecuted": "Slack17"}, "executionData": {"contextData": {}, "nodeExecutionStack": [], "metadata": {}, "waitingExecution": {}, "waitingExecutionSource": {}}}, "mode": "cli", "startedAt": "2025-05-15T21:20:02.189Z", "stoppedAt": "2025-05-15T21:20:04.559Z", "status": "running", "finished": true}