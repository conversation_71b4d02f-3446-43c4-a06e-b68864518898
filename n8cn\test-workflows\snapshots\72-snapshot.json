{"data": {"startData": {}, "resultData": {"runData": {"Start": [{"startTime": 1676891396434, "executionTime": 0, "source": [], "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "Read Binary File": [{"startTime": 1676891396435, "executionTime": 24, "source": [{"previousNode": "Start"}], "executionStatus": "success", "data": {"main": [[{"json": {}, "binary": {"data": {"mimeType": "image/png", "fileType": "image", "fileExtension": "png", "data": "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", "directory": "/tmp", "fileName": "n8n-logo.png", "fileSize": "2.67 kB"}}, "pairedItem": {"item": 0}}]]}}], "Zulip": [{"startTime": 1676891396459, "executionTime": 475, "source": [{"previousNode": "Start"}], "executionStatus": "success", "data": {"main": [[{"json": {"result": "success", "msg": "", "id": *********}, "pairedItem": {"item": 0}}]]}}], "Zulip7": [{"startTime": 1676891396934, "executionTime": 376, "source": [{"previousNode": "Start"}], "executionStatus": "success", "data": {"main": [[{"json": {"result": "success", "msg": "", "subscribed": {"object": true}, "already_subscribed": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "Zulip12": [{"startTime": 1676891397310, "executionTime": 332, "source": [{"previousNode": "Start"}], "executionStatus": "success", "data": {"main": [[{"json": {"email": "<EMAIL>", "user_id": 392595, "avatar_version": 1, "is_admin": true, "is_owner": true, "is_guest": false, "is_billing_admin": false, "role": 100, "is_bot": false, "full_name": "nodeqa", "timezone": "Europe/Paris", "is_active": true, "date_joined": "2021-02-25T14:05:26.897277+00:00", "avatar_url": {"object": true}, "delivery_email": "<EMAIL>"}, "pairedItem": {"item": 0}}]]}}], "Zulip6": [{"startTime": 1676891397643, "executionTime": 516, "source": [{"previousNode": "Read Binary File"}], "executionStatus": "success", "data": {"main": [[{"json": {"result": "success", "msg": "", "uri": "https://nodeqa.zulipchat.com//user_uploads/33385/j5pTnm6OfvyVgGiNXDVJ2Lkm/n8n-logo.png"}, "pairedItem": {"item": 0}}]]}}], "Zulip1": [{"startTime": 1676891398160, "executionTime": 478, "source": [{"previousNode": "Zulip"}], "executionStatus": "success", "data": {"main": [[{"json": {"result": "success", "msg": ""}, "pairedItem": {"item": 0}}]]}}], "Zulip8": [{"startTime": 1676891398638, "executionTime": 424, "source": [{"previousNode": "Zulip7"}], "executionStatus": "success", "data": {"main": [[{"json": {"can_remove_subscribers_group_id": 35057, "date_created": 1676891397, "description": "testing stream from n8n", "first_message_id": 328952616, "history_public_to_subscribers": true, "invite_only": false, "is_web_public": false, "message_retention_days": {"object": true}, "name": "StreamTest", "rendered_description": "<p>testing stream from n8n</p>", "stream_id": 371131, "stream_post_policy": 1, "is_announcement_only": false}, "pairedItem": {"item": 0}}, {"json": {"can_remove_subscribers_group_id": 35057, "date_created": 1614934173, "description": "testing stream from n8n", "first_message_id": 228916381, "history_public_to_subscribers": true, "invite_only": false, "is_web_public": false, "message_retention_days": {"object": true}, "name": "UpdateStream1614934177572", "rendered_description": "<p>testing stream from n8n</p>", "stream_id": 279988, "stream_post_policy": 1, "is_announcement_only": false}, "pairedItem": {"item": 0}}, {"json": {"can_remove_subscribers_group_id": 35057, "date_created": 1614934197, "description": "testing stream from n8n", "first_message_id": 228916526, "history_public_to_subscribers": true, "invite_only": false, "is_web_public": false, "message_retention_days": {"object": true}, "name": "UpdateStream1614934202284", "rendered_description": "<p>testing stream from n8n</p>", "stream_id": 280046, "stream_post_policy": 1, "is_announcement_only": false}, "pairedItem": {"item": 0}}, {"json": {"can_remove_subscribers_group_id": 35057, "date_created": 1614261926, "description": "Everyone is added to this stream by default. Welcome! :octopus:", "first_message_id": 227771365, "history_public_to_subscribers": true, "invite_only": false, "is_web_public": false, "message_retention_days": {"object": true}, "name": "general", "rendered_description": "<p>Everyone is added to this stream by default. Welcome! <span aria-label=\"octopus\" class=\"emoji emoji-1f419\" role=\"img\" title=\"octopus\">:octopus:</span></p>", "stream_id": 278952, "stream_post_policy": 1, "is_announcement_only": false}, "pairedItem": {"item": 0}}, {"json": {"can_remove_subscribers_group_id": 35057, "date_created": 1614262281, "description": "testing stream", "first_message_id": 227772218, "history_public_to_subscribers": true, "invite_only": true, "is_web_public": false, "message_retention_days": {"object": true}, "name": "test", "rendered_description": "<p>testing stream</p>", "stream_id": 278954, "stream_post_policy": 1, "is_announcement_only": false}, "pairedItem": {"item": 0}}]]}}], "Zulip10": [{"startTime": 1676891399062, "executionTime": 434, "source": [{"previousNode": "Zulip7"}], "executionStatus": "success", "data": {"main": [[{"json": {"audible_notifications": {"object": true}, "can_remove_subscribers_group_id": 35057, "color": "#c2c2c2", "date_created": 1676891397, "description": "testing stream from n8n", "desktop_notifications": {"object": true}, "email_address": "<EMAIL>", "email_notifications": {"object": true}, "first_message_id": 328952616, "history_public_to_subscribers": true, "in_home_view": true, "invite_only": false, "is_announcement_only": false, "is_muted": false, "is_web_public": false, "message_retention_days": {"object": true}, "name": "StreamTest", "pin_to_top": false, "push_notifications": {"object": true}, "rendered_description": "<p>testing stream from n8n</p>", "stream_id": 371131, "stream_post_policy": 1, "stream_weekly_traffic": {"object": true}, "wildcard_mentions_notify": {"object": true}}, "pairedItem": {"item": 0}}, {"json": {"audible_notifications": {"object": true}, "can_remove_subscribers_group_id": 35057, "color": "#b0a5fd", "date_created": 1614934173, "description": "testing stream from n8n", "desktop_notifications": {"object": true}, "email_address": "<EMAIL>", "email_notifications": {"object": true}, "first_message_id": 228916381, "history_public_to_subscribers": true, "in_home_view": true, "invite_only": false, "is_announcement_only": false, "is_muted": false, "is_web_public": false, "message_retention_days": {"object": true}, "name": "UpdateStream1614934177572", "pin_to_top": false, "push_notifications": {"object": true}, "rendered_description": "<p>testing stream from n8n</p>", "stream_id": 279988, "stream_post_policy": 1, "stream_weekly_traffic": 0, "wildcard_mentions_notify": {"object": true}}, "pairedItem": {"item": 0}}, {"json": {"audible_notifications": {"object": true}, "can_remove_subscribers_group_id": 35057, "color": "#addfe5", "date_created": 1614934197, "description": "testing stream from n8n", "desktop_notifications": {"object": true}, "email_address": "<EMAIL>", "email_notifications": {"object": true}, "first_message_id": 228916526, "history_public_to_subscribers": true, "in_home_view": true, "invite_only": false, "is_announcement_only": false, "is_muted": false, "is_web_public": false, "message_retention_days": {"object": true}, "name": "UpdateStream1614934202284", "pin_to_top": false, "push_notifications": {"object": true}, "rendered_description": "<p>testing stream from n8n</p>", "stream_id": 280046, "stream_post_policy": 1, "stream_weekly_traffic": 0, "wildcard_mentions_notify": {"object": true}}, "pairedItem": {"item": 0}}, {"json": {"audible_notifications": {"object": true}, "can_remove_subscribers_group_id": 35057, "color": "#a6c7e5", "date_created": 1614262281, "description": "testing stream", "desktop_notifications": {"object": true}, "email_address": "<EMAIL>", "email_notifications": {"object": true}, "first_message_id": 227772218, "history_public_to_subscribers": true, "in_home_view": true, "invite_only": true, "is_announcement_only": false, "is_muted": false, "is_web_public": false, "message_retention_days": {"object": true}, "name": "test", "pin_to_top": false, "push_notifications": {"object": true}, "rendered_description": "<p>testing stream</p>", "stream_id": 278954, "stream_post_policy": 1, "stream_weekly_traffic": 0, "wildcard_mentions_notify": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "Zulip13": [{"startTime": 1676891399496, "executionTime": 420, "source": [{"previousNode": "Zulip12"}], "executionStatus": "success", "data": {"main": [[{"json": {"result": "success", "msg": "", "user": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "Zulip2": [{"startTime": 1676891399916, "executionTime": 447, "source": [{"previousNode": "Zulip1"}], "executionStatus": "success", "data": {"main": [[{"json": {"result": "success", "msg": "", "id": 328952630}, "pairedItem": {"item": 0}}]]}}], "Function": [{"startTime": 1676891400363, "executionTime": 9, "source": [{"previousNode": "Zulip8"}], "executionStatus": "success", "data": {"main": [[{"json": {"can_remove_subscribers_group_id": 35057, "date_created": 1676891397, "description": "testing stream from n8n", "first_message_id": 328952616, "history_public_to_subscribers": true, "invite_only": false, "is_web_public": false, "message_retention_days": {"object": true}, "name": "StreamTest", "rendered_description": "<p>testing stream from n8n</p>", "stream_id": 371131, "stream_post_policy": 1, "is_announcement_only": false}, "pairedItem": {"item": 0}, "index": 0}]]}}], "Zulip14": [{"startTime": 1676891400373, "executionTime": 0, "source": [{"previousNode": "Zulip13"}], "executionStatus": "success", "data": {"main": [[{"json": {"result": "success", "msg": "", "user": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "Zulip3": [{"startTime": 1676891400373, "executionTime": 435, "source": [{"previousNode": "Zulip2"}], "executionStatus": "success", "data": {"main": [[{"json": {"result": "success", "msg": "", "message": {"object": true}, "raw_content": "Update content 1676891398165"}, "pairedItem": {"item": 0}}]]}}], "Zulip9": [{"startTime": 1676891400808, "executionTime": 375, "source": [{"previousNode": "Function"}], "executionStatus": "success", "data": {"main": [[{"json": {"result": "success", "msg": ""}, "pairedItem": {"item": 0}}]]}}], "Zulip15": [{"startTime": 1676891401184, "executionTime": 0, "source": [{"previousNode": "Zulip14"}], "executionStatus": "success", "data": {"main": [[{"json": {"result": "success", "msg": "", "user": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "Zulip4": [{"startTime": 1676891401184, "executionTime": 374, "source": [{"previousNode": "Zulip3"}], "executionStatus": "success", "data": {"main": [[{"json": {"result": "success", "msg": ""}, "pairedItem": {"item": 0}}]]}}], "Zulip11": [{"startTime": 1676891401558, "executionTime": 431, "source": [{"previousNode": "Zulip9"}], "executionStatus": "success", "data": {"main": [[{"json": {"result": "success", "msg": ""}, "pairedItem": {"item": 0}}]]}}], "Zulip5": [{"startTime": 1676891401989, "executionTime": 483, "source": [{"previousNode": "Zulip4"}], "executionStatus": "success", "data": {"main": [[{"json": {"result": "success", "msg": ""}, "pairedItem": {"item": 0}}]]}}]}, "lastNodeExecuted": "Zulip5"}, "executionData": {"contextData": {}, "nodeExecutionStack": [], "waitingExecution": {}, "waitingExecutionSource": {}}}, "mode": "cli", "startedAt": "2023-02-20T11:09:56.431Z", "stoppedAt": "2023-02-20T11:10:02.472Z", "status": "running", "finished": true}