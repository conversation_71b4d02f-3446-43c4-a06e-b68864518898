{"data": {"startData": {}, "resultData": {"runData": {"Start": [{"startTime": 1747343994616, "executionIndex": 0, "source": [], "hints": [], "executionTime": 0, "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "AWS SQS1": [{"startTime": 1747343994616, "executionIndex": 1, "source": [{"previousNode": "Start"}], "hints": [], "executionTime": 477, "executionStatus": "success", "data": {"main": [[{"json": {"MessageId": "d7d01c99-6045-4687-aa22-3696b28a71dc", "MD5OfMessageBody": "b5c549a1b61608ae9c9e5aa4ca926eaf", "MD5OfMessageAttributes": "563c033205d4d7ddc71dceef55cae220", "SequenceNumber": "184914760783929180160"}, "pairedItem": {"item": 0}}]]}}], "AWS SQS": [{"startTime": 1747343995093, "executionIndex": 2, "source": [{"previousNode": "Start"}], "hints": [], "executionTime": 458, "executionStatus": "success", "data": {"main": [[{"json": {"MessageId": "002ae7dd-846a-4ca2-a0d6-bd95aa9ca6b0", "MD5OfMessageBody": "a6b50b8dda796ce940cd81ac4078f1e0", "MD5OfMessageAttributes": "563c033205d4d7ddc71dceef55cae220"}, "pairedItem": {"item": 0}}]]}}]}, "lastNodeExecuted": "AWS SQS"}, "executionData": {"contextData": {}, "nodeExecutionStack": [], "metadata": {}, "waitingExecution": {}, "waitingExecutionSource": {}}}, "mode": "cli", "startedAt": "2025-05-15T21:19:54.616Z", "stoppedAt": "2025-05-15T21:19:55.551Z", "status": "running", "finished": true}