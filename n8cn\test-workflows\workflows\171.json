{"createdAt": "2021-04-19T10:15:58.189Z", "updatedAt": "2021-06-04T14:55:29.008Z", "id": "171", "name": "Stackby:append read list delete", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300], "id": "570ac713-7639-4d31-aed5-d813b6cb3f32"}, {"parameters": {"stackId": "stOgji6AOGIluLfusu", "table": "TestTable", "columns": "id,name"}, "name": "Stackby", "type": "n8n-nodes-base.stackby", "typeVersion": 1, "position": [570, 300], "credentials": {"stackbyApi": {"id": "140", "name": "Stackby API creds"}}, "id": "c5684dac-5284-4aee-ac91-fbf9cd9f0ee6"}, {"parameters": {"values": {"number": [{"name": "id", "value": "={{Math.round(Math.random()*1000)}}"}], "string": [{"name": "name", "value": "=Test{{Date.now()}}"}]}, "options": {}}, "name": "Set", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [400, 300], "id": "31ebc435-4f59-42a7-813d-6d98eb59449e"}, {"parameters": {"operation": "read", "stackId": "stOgji6AOGIluLfusu", "table": "TestTable", "id": "={{$node[\"Stackby\"].json[\"rowId\"]}}"}, "name": "Stackby1", "type": "n8n-nodes-base.stackby", "typeVersion": 1, "position": [720, 300], "credentials": {"stackbyApi": {"id": "140", "name": "Stackby API creds"}}, "id": "d78e9f09-6930-46d9-919b-80bfaeeac953"}, {"parameters": {"operation": "list", "stackId": "stOgji6AOGIluLfusu", "table": "TestTable", "additionalFields": {"view": ""}}, "name": "Stackby2", "type": "n8n-nodes-base.stackby", "typeVersion": 1, "position": [880, 300], "credentials": {"stackbyApi": {"id": "140", "name": "Stackby API creds"}}, "notes": "CAP_RESULTS_LENGTH=1", "id": "e94a8735-e82b-4388-8020-925e6e549c1b"}, {"parameters": {"operation": "delete", "stackId": "stOgji6AOGIluLfusu", "table": "TestTable", "id": "={{$node[\"Stackby\"].json[\"rowId\"]}}"}, "name": "Stackby3", "type": "n8n-nodes-base.stackby", "typeVersion": 1, "position": [1030, 300], "executeOnce": true, "credentials": {"stackbyApi": {"id": "140", "name": "Stackby API creds"}}, "id": "18174a44-9f60-4a2b-9635-1f5f5eb579c7"}], "connections": {"Stackby": {"main": [[{"node": "Stackby1", "type": "main", "index": 0}]]}, "Set": {"main": [[{"node": "Stackby", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "Set", "type": "main", "index": 0}]]}, "Stackby1": {"main": [[{"node": "Stackby2", "type": "main", "index": 0}]]}, "Stackby2": {"main": [[{"node": "Stackby3", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}