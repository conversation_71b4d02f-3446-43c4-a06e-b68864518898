{"data": {"startData": {}, "resultData": {"runData": {"Start": [{"hints": [], "startTime": 1726655395077, "executionTime": 0, "source": [], "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "Mailjet": [{"hints": [], "startTime": 1726655395077, "executionTime": 146, "source": [{"previousNode": "Start"}], "executionStatus": "success", "data": {"main": [[{"json": {"Status": "success", "CustomID": "", "To": ["json array"], "Cc": ["json array"], "Bcc": ["json array"]}, "pairedItem": {"item": 0}}]]}}], "Mailjet2": [{"hints": [], "startTime": 1726655395223, "executionTime": 0, "source": [{"previousNode": "Start"}], "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "Mailjet1": [{"hints": [], "startTime": 1726655395223, "executionTime": 275, "source": [{"previousNode": "Mailjet"}], "executionStatus": "success", "data": {"main": [[{"json": {"Status": "success", "CustomID": "", "To": ["json array"], "Cc": ["json array"], "Bcc": ["json array"]}, "pairedItem": {"item": 0}}]]}}]}, "lastNodeExecuted": "Mailjet1"}, "executionData": {"contextData": {}, "nodeExecutionStack": [], "metadata": {}, "waitingExecution": {}, "waitingExecutionSource": {}}}, "mode": "cli", "startedAt": "2024-09-18T10:29:55.077Z", "stoppedAt": "2024-09-18T10:29:55.498Z", "status": "running", "finished": true}