---
title: N8N Enterprise Edition
emoji: 🚀
colorFrom: blue
colorTo: purple
sdk: docker
pinned: false
---

# N8N 企业版 - 工作流自动化平台

这是一个基于 [deluxebear/n8n](https://github.com/deluxebear/n8n/tree/chs) 企业版的 n8n 部署，提供完整的中文界面和企业级功能。

## ✨ 企业版功能特性

### 🔐 认证和安全
- ✅ SAML单点登录
- ✅ LDAP/Active Directory集成  
- ✅ OpenID Connect/OAuth 2.0
- ✅ 高级权限管理
- ✅ API密钥作用域

### 👥 协作和项目管理
- ✅ 工作流和凭证共享
- ✅ 项目管理员/编辑者/查看者角色
- ✅ 文件夹组织
- ✅ 团队协作功能

### 🛠️ 开发运维
- ✅ Git源代码控制
- ✅ 环境变量管理
- ✅ 外部密钥管理
- ✅ 工作流版本历史
- ✅ 编辑器调试功能

### 🏗️ 基础设施和扩展
- ✅ 多主实例高可用
- ✅ S3二进制数据存储
- ✅ 工作节点监控
- ✅ 日志流式传输
- ✅ 高级执行过滤器

### 🤖 AI和分析
- ✅ AI助手
- ✅ AI问答功能
- ✅ AI积分系统
- ✅ 洞察摘要视图
- ✅ 洞察仪表板

## 🚀 部署配置

### 环境变量配置

在 Hugging Face Space 的 Settings 中配置以下环境变量：

#### 数据库配置 (必需)
```bash
POSTGRES_USER=your_postgres_user
POSTGRES_PASSWORD=your_postgres_password  
POSTGRES_DB=your_database_name
POSTGRESDB_HOST=your_postgres_host
```

#### 应用配置 (可选)
```bash
WEBHOOK_URL=https://你的hf账户名-空间名.hf.space/
```

### 数据库支持

支持以下数据库：
- ✅ PostgreSQL (推荐)
- ✅ MySQL
- ✅ MariaDB

### 集成服务

本部署包含以下集成服务：
- **Redis**: 缓存和队列管理
- **Qdrant**: 向量数据库，支持AI功能
- **PostgreSQL**: 主数据库

## 📖 使用指南

### 首次访问

1. 部署完成后，访问您的 Hugging Face Space URL
2. 创建管理员账户
3. 开始创建您的第一个工作流

### 企业功能使用

#### 用户管理
1. 进入 Settings > Users & Roles
2. 邀请团队成员并分配角色
3. 配置权限和访问控制

#### 项目管理
1. 创建项目文件夹
2. 组织工作流和凭证
3. 设置项目级别的权限

#### Git集成
1. 进入 Settings > Source Control
2. 连接您的Git仓库
3. 启用版本控制和协作

#### AI助手
1. 在工作流编辑器中点击AI助手图标
2. 获取智能建议和帮助
3. 使用AI生成工作流代码

## 🔧 技术规格

- **基础镜像**: ghcr.io/deluxebear/n8n:chs
- **Node.js版本**: 20.x
- **数据库**: PostgreSQL
- **缓存**: Redis
- **向量数据库**: Qdrant
- **语言**: 中文 (zh-CN)

## 📚 相关资源

- [N8N 官方文档](https://docs.n8n.io)
- [企业版功能说明](https://github.com/deluxebear/n8n/blob/chs/ENTERPRISE-MOCK-README.md)
- [400+ 集成节点](https://n8n.io/integrations)
- [工作流模板](https://n8n.io/workflows)
- [社区论坛](https://community.n8n.io)

## ⚠️ 重要说明

- 本部署使用企业版功能模拟，仅供测试和学习使用
- 生产环境请购买正式的 n8n 企业版许可证
- 请确保遵守相关的开源许可证条款

## 🆘 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库环境变量配置
   - 确认数据库服务可访问

2. **企业功能未启用**
   - 确认 `N8N_ENTERPRISE_MOCK=true` 已设置
   - 重启应用

3. **中文界面未显示**
   - 确认 `N8N_DEFAULT_LOCALE=zh-CN` 已设置
   - 清除浏览器缓存

### 日志查看

在 Hugging Face Space 的 Logs 标签页中查看应用日志，寻找错误信息。

---

**享受您的 N8N 企业版体验！** 🎉
