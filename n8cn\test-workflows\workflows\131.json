{"createdAt": "2021-03-12T15:30:32.231Z", "updatedAt": "2021-03-12T15:30:32.231Z", "id": "131", "name": "Line:Notification:send", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300], "id": "b97b22de-c820-46c3-bd9a-9ccd77d9fb2f"}, {"parameters": {"message": "=Notification{{Date.now()}}", "additionalFields": {}}, "name": "Line", "type": "n8n-nodes-base.line", "typeVersion": 1, "position": [480, 300], "credentials": {"lineNotifyOAuth2Api": {"id": "99", "name": "Line Notify OAuth2 API"}}, "id": "bda3f612-99db-4f50-86ec-71f3f7e26917"}], "connections": {"Start": {"main": [[{"node": "Line", "type": "main", "index": 0}]]}, "Line": {"main": [[]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}