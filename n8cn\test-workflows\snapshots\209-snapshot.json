{"data": {"startData": {}, "resultData": {"runData": {"Start": [{"startTime": 1747343995231, "executionIndex": 0, "source": [], "hints": [], "executionTime": 0, "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "Set job name": [{"startTime": 1747343995231, "executionIndex": 1, "source": [{"previousNode": "Start"}], "hints": [], "executionTime": 1, "executionStatus": "success", "data": {"main": [[{"json": {"job_name": "nodemation_job_1747343995232"}, "pairedItem": {"item": 0}}]]}}], "AWS Transcribe": [{"startTime": 1747343995232, "executionIndex": 2, "source": [{"previousNode": "Set job name"}], "hints": [], "executionTime": 539, "executionStatus": "success", "data": {"main": [[{"json": {"CreationTime": 1747343995.631, "LanguageCode": "en-US", "Media": {"object": true}, "Settings": {"object": true}, "StartTime": 1747343995.653, "TranscriptionJobName": "nodemation_job_1747343995232", "TranscriptionJobStatus": "IN_PROGRESS"}, "pairedItem": {"item": 0}}]]}}], "AWS Transcribe1": [{"startTime": 1747343995771, "executionIndex": 3, "source": [{"previousNode": "AWS Transcribe"}], "hints": [], "executionTime": 368, "executionStatus": "success", "data": {"main": [[{"json": {"CreationTime": 1747343995.631, "LanguageCode": "en-US", "Media": {"object": true}, "Settings": {"object": true}, "StartTime": 1747343995.653, "Transcript": {"object": true}, "TranscriptionJobName": "nodemation_job_1747343995232", "TranscriptionJobStatus": "IN_PROGRESS"}, "pairedItem": {"item": 0}}]]}}], "AWS Transcribe2": [{"startTime": 1747343996139, "executionIndex": 4, "source": [{"previousNode": "AWS Transcribe1"}], "hints": [], "executionTime": 551, "executionStatus": "success", "data": {"main": [[{"json": {"CreationTime": 1747343995.631, "LanguageCode": "en-US", "OutputLocationType": "SERVICE_BUCKET", "StartTime": 1747343995.653, "TranscriptionJobName": "nodemation_job_1747343995232", "TranscriptionJobStatus": "IN_PROGRESS"}, "pairedItem": {"item": 0}}]]}}], "Sleep 30 seconds": [{"startTime": 1747343996690, "executionIndex": 5, "source": [{"previousNode": "AWS Transcribe2"}], "hints": [], "executionTime": 30001, "executionStatus": "success", "data": {"main": [[{"json": {"CreationTime": 1747343995.631, "LanguageCode": "en-US", "OutputLocationType": "SERVICE_BUCKET", "StartTime": 1747343995.653, "TranscriptionJobName": "nodemation_job_1747343995232", "TranscriptionJobStatus": "IN_PROGRESS"}, "pairedItem": {"item": 0}, "index": 0}]]}}], "AWS Transcribe3": [{"startTime": 1747344026691, "executionIndex": 6, "source": [{"previousNode": "Sleep 30 seconds"}], "hints": [], "executionTime": 440, "executionStatus": "success", "data": {"main": [[{"json": {"success": true}, "pairedItem": {"item": 0}}]]}}]}, "lastNodeExecuted": "AWS Transcribe3"}, "executionData": {"contextData": {}, "nodeExecutionStack": [], "metadata": {}, "waitingExecution": {}, "waitingExecutionSource": {}}}, "mode": "cli", "startedAt": "2025-05-15T21:19:55.231Z", "stoppedAt": "2025-05-15T21:20:27.131Z", "status": "running", "finished": true}