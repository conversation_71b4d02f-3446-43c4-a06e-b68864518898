{"createdAt": "2021-04-19T09:55:16.357Z", "updatedAt": "2021-05-25T13:38:12.376Z", "id": "170", "name": "Xero:Contact:create get getAll update:Invoice:create get getAll update", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300], "id": "f0c45c75-abab-4369-b823-165926299d4d"}, {"parameters": {"resource": "contact", "organizationId": "90ee722a-8780-44f6-8788-e1d6b0bd2aa4", "name": "=Contact{{Date.now()}}", "additionalFields": {"firstName": "=FirstName{{Date.now()}}"}}, "name": "Xero", "type": "n8n-nodes-base.xero", "typeVersion": 1, "position": [500, 200], "credentials": {"xeroOAuth2Api": {"id": "139", "name": "Xero OAuth2 API creds"}}, "id": "bef8a1fb-6608-4881-843a-3f1a0bf6d0df"}, {"parameters": {"resource": "contact", "operation": "get", "organizationId": "90ee722a-8780-44f6-8788-e1d6b0bd2aa4", "contactId": "={{$node[\"Xero\"].json[\"ContactID\"]}}"}, "name": "Xero1", "type": "n8n-nodes-base.xero", "typeVersion": 1, "position": [650, 200], "credentials": {"xeroOAuth2Api": {"id": "139", "name": "Xero OAuth2 API creds"}}, "id": "04550831-0f26-471a-ac9d-4acd8d25e905"}, {"parameters": {"resource": "contact", "operation": "getAll", "organizationId": "90ee722a-8780-44f6-8788-e1d6b0bd2aa4", "limit": 1, "options": {"orderBy": "UpdatedDateUTC", "sortOrder": "ASC"}}, "name": "Xero2", "type": "n8n-nodes-base.xero", "typeVersion": 1, "position": [800, 200], "credentials": {"xeroOAuth2Api": {"id": "139", "name": "Xero OAuth2 API creds"}}, "id": "db15691c-4eb1-4ada-81c0-deb62b1ad238"}, {"parameters": {"resource": "contact", "operation": "update", "organizationId": "90ee722a-8780-44f6-8788-e1d6b0bd2aa4", "contactId": "={{$node[\"Xero\"].json[\"ContactID\"]}}", "updateFields": {"firstName": "=Updated{{$node[\"Xero1\"].json[\"FirstName\"]}}"}}, "name": "Xero3", "type": "n8n-nodes-base.xero", "typeVersion": 1, "position": [950, 200], "credentials": {"xeroOAuth2Api": {"id": "139", "name": "Xero OAuth2 API creds"}}, "id": "8ca6dfd8-8429-4ae8-809e-7f2167350cf2"}, {"parameters": {"organizationId": "90ee722a-8780-44f6-8788-e1d6b0bd2aa4", "type": "ACCPAY", "contactId": "={{$node[\"Xero1\"].json[\"ContactID\"]}}", "lineItemsUi": {"lineItemsValues": [{"description": "Test", "quantity": 3, "unitAmount": "10", "itemCode": "BOOK", "accountCode": "300", "taxType": "INPUT", "taxAmount": "10"}]}, "additionalFields": {}}, "name": "Xero4", "type": "n8n-nodes-base.xero", "typeVersion": 1, "position": [800, 350], "credentials": {"xeroOAuth2Api": {"id": "139", "name": "Xero OAuth2 API creds"}}, "id": "fd0adcca-b10b-4ae1-9a24-d088339623f7"}, {"parameters": {"operation": "get", "organizationId": "90ee722a-8780-44f6-8788-e1d6b0bd2aa4", "invoiceId": "={{$node[\"Xero4\"].json[\"InvoiceID\"]}}"}, "name": "Xero5", "type": "n8n-nodes-base.xero", "typeVersion": 1, "position": [950, 350], "credentials": {"xeroOAuth2Api": {"id": "139", "name": "Xero OAuth2 API creds"}}, "id": "576e9017-aabf-4651-b653-fa13d56de323"}, {"parameters": {"operation": "getAll", "organizationId": "90ee722a-8780-44f6-8788-e1d6b0bd2aa4", "limit": 1, "options": {}}, "name": "Xero6", "type": "n8n-nodes-base.xero", "typeVersion": 1, "position": [1100, 350], "credentials": {"xeroOAuth2Api": {"id": "139", "name": "Xero OAuth2 API creds"}}, "id": "fe2004ce-131e-448a-90c5-8c326a30b2dd"}, {"parameters": {"operation": "update", "organizationId": "90ee722a-8780-44f6-8788-e1d6b0bd2aa4", "invoiceId": "={{$node[\"Xero4\"].json[\"InvoiceID\"]}}", "updateFields": {"dueDate": "2021-04-29T22:00:00.000Z", "status": "SUBMITTED"}}, "name": "Xero7", "type": "n8n-nodes-base.xero", "typeVersion": 1, "position": [1250, 350], "credentials": {"xeroOAuth2Api": {"id": "139", "name": "Xero OAuth2 API creds"}}, "id": "1e653ae8-8157-4d05-9dd2-2446a64385d1"}], "connections": {"Xero": {"main": [[{"node": "Xero1", "type": "main", "index": 0}]]}, "Xero1": {"main": [[{"node": "Xero2", "type": "main", "index": 0}, {"node": "Xero4", "type": "main", "index": 0}]]}, "Xero2": {"main": [[{"node": "Xero3", "type": "main", "index": 0}]]}, "Xero4": {"main": [[{"node": "Xero5", "type": "main", "index": 0}]]}, "Xero5": {"main": [[{"node": "Xero6", "type": "main", "index": 0}]]}, "Xero6": {"main": [[{"node": "Xero7", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "Xero", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}