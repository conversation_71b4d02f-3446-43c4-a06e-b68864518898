{"data": {"startData": {}, "resultData": {"runData": {"Start": [{"startTime": 1676891405872, "executionTime": 1, "source": [], "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "Ghost": [{"startTime": 1676891405873, "executionTime": 475, "source": [{"previousNode": "Start"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": "63f3550e3464df0001bf1bbc", "uuid": "25ff3cb7-725f-478d-9710-8798ce3ceccd", "title": "PostTitle1676891405907", "slug": "posttitle1676891405907", "mobiledoc": "{\"version\":\"0.3.1\",\"atoms\":[],\"cards\":[],\"markups\":[],\"sections\":[[1,\"p\",[[0,[],0,\"Post Content written at 1676891405907\"]]]]}", "comment_id": "63f3550e3464df0001bf1bbc", "feature_image": {"object": true}, "featured": false, "status": "draft", "visibility": "public", "created_at": "2023-02-20T11:10:06.000Z", "updated_at": "2023-02-20T11:10:06.000Z", "published_at": {"object": true}, "custom_excerpt": {"object": true}, "codeinjection_head": {"object": true}, "codeinjection_foot": {"object": true}, "custom_template": {"object": true}, "canonical_url": {"object": true}, "authors": ["json array"], "tags": ["json array"], "primary_author": {"object": true}, "primary_tag": {"object": true}, "url": "http://localhost:2368/p/25ff3cb7-725f-478d-9710-8798ce3ceccd/", "excerpt": "Post Content written at 1676891405907", "og_image": {"object": true}, "og_title": {"object": true}, "og_description": {"object": true}, "twitter_image": {"object": true}, "twitter_title": {"object": true}, "twitter_description": {"object": true}, "meta_title": {"object": true}, "meta_description": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "Ghost1": [{"startTime": 1676891406348, "executionTime": 386, "source": [{"previousNode": "Ghost"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": "63f3550e3464df0001bf1bbc", "uuid": "25ff3cb7-725f-478d-9710-8798ce3ceccd", "title": "UpdateTitle1676891406349", "slug": "posttitle1676891405907", "mobiledoc": "{\"version\":\"0.3.1\",\"atoms\":[],\"cards\":[],\"markups\":[],\"sections\":[[1,\"p\",[[0,[],0,\"Post Content written at 1676891405907\"]]]]}", "comment_id": "63f3550e3464df0001bf1bbc", "feature_image": {"object": true}, "featured": false, "status": "draft", "visibility": "public", "created_at": "2023-02-20T11:10:06.000Z", "updated_at": "2023-02-20T11:10:06.000Z", "published_at": {"object": true}, "custom_excerpt": {"object": true}, "codeinjection_head": {"object": true}, "codeinjection_foot": {"object": true}, "custom_template": {"object": true}, "canonical_url": {"object": true}, "tags": ["json array"], "authors": ["json array"], "primary_author": {"object": true}, "primary_tag": {"object": true}, "url": "http://localhost:2368/p/25ff3cb7-725f-478d-9710-8798ce3ceccd/", "excerpt": "Post Content written at 1676891405907", "og_image": {"object": true}, "og_title": {"object": true}, "og_description": {"object": true}, "twitter_image": {"object": true}, "twitter_title": {"object": true}, "twitter_description": {"object": true}, "meta_title": {"object": true}, "meta_description": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "Ghost2": [{"startTime": 1676891406734, "executionTime": 248, "source": [{"previousNode": "Ghost1"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": "63f3550e3464df0001bf1bbc", "uuid": "25ff3cb7-725f-478d-9710-8798ce3ceccd", "title": "UpdateTitle1676891406349", "slug": "posttitle1676891405907", "mobiledoc": "{\"version\":\"0.3.1\",\"atoms\":[],\"cards\":[],\"markups\":[],\"sections\":[[1,\"p\",[[0,[],0,\"Post Content written at 1676891405907\"]]]]}", "comment_id": "63f3550e3464df0001bf1bbc", "feature_image": {"object": true}, "featured": false, "status": "draft", "visibility": "public", "created_at": "2023-02-20T11:10:06.000Z", "updated_at": "2023-02-20T11:10:06.000Z", "published_at": {"object": true}, "custom_excerpt": {"object": true}, "codeinjection_head": {"object": true}, "codeinjection_foot": {"object": true}, "custom_template": {"object": true}, "canonical_url": {"object": true}, "tags": ["json array"], "authors": ["json array"], "primary_author": {"object": true}, "primary_tag": {"object": true}, "url": "http://localhost:2368/p/25ff3cb7-725f-478d-9710-8798ce3ceccd/", "excerpt": "Post Content written at 1676891405907", "og_image": {"object": true}, "og_title": {"object": true}, "og_description": {"object": true}, "twitter_image": {"object": true}, "twitter_title": {"object": true}, "twitter_description": {"object": true}, "meta_title": {"object": true}, "meta_description": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "Ghost3": [{"startTime": 1676891406983, "executionTime": 244, "source": [{"previousNode": "Ghost2"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": "63f3550e3464df0001bf1bbc", "uuid": "25ff3cb7-725f-478d-9710-8798ce3ceccd", "title": "UpdateTitle1676891406349", "slug": "posttitle1676891405907", "mobiledoc": "{\"version\":\"0.3.1\",\"atoms\":[],\"cards\":[],\"markups\":[],\"sections\":[[1,\"p\",[[0,[],0,\"Post Content written at 1676891405907\"]]]]}", "comment_id": "63f3550e3464df0001bf1bbc", "feature_image": {"object": true}, "featured": false, "status": "draft", "visibility": "public", "created_at": "2023-02-20T11:10:06.000Z", "updated_at": "2023-02-20T11:10:06.000Z", "published_at": {"object": true}, "custom_excerpt": {"object": true}, "codeinjection_head": {"object": true}, "codeinjection_foot": {"object": true}, "custom_template": {"object": true}, "canonical_url": {"object": true}, "tags": ["json array"], "authors": ["json array"], "primary_author": {"object": true}, "primary_tag": {"object": true}, "url": "http://localhost:2368/p/25ff3cb7-725f-478d-9710-8798ce3ceccd/", "excerpt": "Post Content written at 1676891405907", "og_image": {"object": true}, "og_title": {"object": true}, "og_description": {"object": true}, "twitter_image": {"object": true}, "twitter_title": {"object": true}, "twitter_description": {"object": true}, "meta_title": {"object": true}, "meta_description": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "Ghost4": [{"startTime": 1676891407227, "executionTime": 164, "source": [{"previousNode": "Ghost3"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": "60489c2d8b68c800011955f0", "uuid": "51c60db4-8ef0-4de9-8529-c1cde7460591", "title": "Welcome to Ghost", "slug": "welcome", "html": "<h2 id=\"a-few-things-you-should-know\"><strong>A few things you should know</strong></h2><ol><li>Ghost is designed for ambitious, professional publishers who want to actively build a business around their content. That's who it works best for. </li><li>The entire platform can be modified and customised to suit your needs. It's very powerful, but does require some knowledge of code. Ghost is not necessarily a good platform for beginners or people who just want a simple personal blog. </li><li>It's possible to work with all your favourite tools and apps with hundreds of <a href=\"https://ghost.org/integrations/\">integrations</a> to speed up your workflows, connect email lists, build communities and much more.</li></ol><h2 id=\"behind-the-scenes\">Behind the scenes</h2><p>Ghost is made by an independent non-profit organisation called the Ghost Foundation. We are 100% self funded by revenue from our <a href=\"https://ghost.org/pricing\">Ghost(Pro)</a> service, and every penny we make is re-invested into funding further development of free, open source technology for modern publishing.</p><p>The version of Ghost you are looking at right now would not have been made possible without generous contributions from the open source <a href=\"https://github.com/TryGhost\">community</a>.</p><h2 id=\"next-up-the-editor\">Next up, the editor</h2><p>The main thing you'll want to read about next is probably: <a href=\"http://localhost:2368/the-editor/\">the Ghost editor</a>. This is where the good stuff happens.</p><blockquote>By the way, once you're done reading, you can simply delete the default Ghost user from your team to remove all of these introductory posts! </blockquote>", "comment_id": "60489c2d8b68c800011955f0", "feature_image": "https://static.ghost.org/v3.0.0/images/welcome-to-ghost.png", "featured": false, "visibility": "public", "email_recipient_filter": "none", "created_at": "2021-03-10T10:15:09.000+00:00", "updated_at": "2021-03-10T10:15:09.000+00:00", "published_at": "2021-03-10T10:15:15.000+00:00", "custom_excerpt": "Welcome, it's great to have you here.\nWe know that first impressions are important, so we've populated your new site with some initial getting started posts that will help you get familiar with everything in no time.", "codeinjection_head": {"object": true}, "codeinjection_foot": {"object": true}, "custom_template": {"object": true}, "canonical_url": {"object": true}, "url": "http://localhost:2368/welcome/", "excerpt": "Welcome, it's great to have you here.\nWe know that first impressions are important, so we've populated your new site with some initial getting started posts that will help you get familiar with everything in no time.", "reading_time": 1, "access": true, "send_email_when_published": false, "og_image": {"object": true}, "og_title": {"object": true}, "og_description": {"object": true}, "twitter_image": {"object": true}, "twitter_title": {"object": true}, "twitter_description": {"object": true}, "meta_title": {"object": true}, "meta_description": {"object": true}, "email_subject": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "Ghost5": [{"startTime": 1676891407391, "executionTime": 182, "source": [{"previousNode": "Ghost4"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": "60489c2d8b68c800011955f0", "uuid": "51c60db4-8ef0-4de9-8529-c1cde7460591", "title": "Welcome to Ghost", "slug": "welcome", "html": "<h2 id=\"a-few-things-you-should-know\"><strong>A few things you should know</strong></h2><ol><li>Ghost is designed for ambitious, professional publishers who want to actively build a business around their content. That's who it works best for. </li><li>The entire platform can be modified and customised to suit your needs. It's very powerful, but does require some knowledge of code. Ghost is not necessarily a good platform for beginners or people who just want a simple personal blog. </li><li>It's possible to work with all your favourite tools and apps with hundreds of <a href=\"https://ghost.org/integrations/\">integrations</a> to speed up your workflows, connect email lists, build communities and much more.</li></ol><h2 id=\"behind-the-scenes\">Behind the scenes</h2><p>Ghost is made by an independent non-profit organisation called the Ghost Foundation. We are 100% self funded by revenue from our <a href=\"https://ghost.org/pricing\">Ghost(Pro)</a> service, and every penny we make is re-invested into funding further development of free, open source technology for modern publishing.</p><p>The version of Ghost you are looking at right now would not have been made possible without generous contributions from the open source <a href=\"https://github.com/TryGhost\">community</a>.</p><h2 id=\"next-up-the-editor\">Next up, the editor</h2><p>The main thing you'll want to read about next is probably: <a href=\"http://localhost:2368/the-editor/\">the Ghost editor</a>. This is where the good stuff happens.</p><blockquote>By the way, once you're done reading, you can simply delete the default Ghost user from your team to remove all of these introductory posts! </blockquote>", "comment_id": "60489c2d8b68c800011955f0", "feature_image": "https://static.ghost.org/v3.0.0/images/welcome-to-ghost.png", "featured": false, "visibility": "public", "email_recipient_filter": "none", "created_at": "2021-03-10T10:15:09.000+00:00", "updated_at": "2021-03-10T10:15:09.000+00:00", "published_at": "2021-03-10T10:15:15.000+00:00", "custom_excerpt": "Welcome, it's great to have you here.\nWe know that first impressions are important, so we've populated your new site with some initial getting started posts that will help you get familiar with everything in no time.", "codeinjection_head": {"object": true}, "codeinjection_foot": {"object": true}, "custom_template": {"object": true}, "canonical_url": {"object": true}, "url": "http://localhost:2368/welcome/", "excerpt": "Welcome, it's great to have you here.\nWe know that first impressions are important, so we've populated your new site with some initial getting started posts that will help you get familiar with everything in no time.", "reading_time": 1, "access": true, "send_email_when_published": false, "og_image": {"object": true}, "og_title": {"object": true}, "og_description": {"object": true}, "twitter_image": {"object": true}, "twitter_title": {"object": true}, "twitter_description": {"object": true}, "meta_title": {"object": true}, "meta_description": {"object": true}, "email_subject": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "Ghost6": [{"startTime": 1676891407573, "executionTime": 170, "source": [{"previousNode": "Ghost5"}], "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}]}, "lastNodeExecuted": "Ghost6"}, "executionData": {"contextData": {}, "nodeExecutionStack": [], "waitingExecution": {}, "waitingExecutionSource": {}}}, "mode": "cli", "startedAt": "2023-02-20T11:10:05.871Z", "stoppedAt": "2023-02-20T11:10:07.743Z", "status": "running", "finished": true}