{"createdAt": "2021-03-04T11:22:05.930Z", "updatedAt": "2021-03-04T11:24:54.336Z", "id": "103", "name": "ExecuteCommand", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300], "id": "596a5418-e61d-41ab-84eb-64e07630b23e"}, {"parameters": {"command": "=echo 'test' > /tmp/{{$node[\"Set\"].json[\"filename\"]}}"}, "name": "Execute Command", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [660, 300], "notesInFlow": true, "notes": "write file to /tmp", "id": "9c118fb2-edad-441a-b8a8-f1c843a37bd5"}, {"parameters": {"filePath": "=/tmp/{{$node[\"Set\"].json[\"filename\"]}}"}, "name": "Read Binary File", "type": "n8n-nodes-base.readBinaryFile", "typeVersion": 1, "position": [860, 300], "notesInFlow": true, "notes": "read file", "id": "af6e6941-e34e-4b5d-a236-3bccd5bd4cfd"}, {"parameters": {"values": {"string": [{"name": "filename", "value": "=filename{{Date.now()}}"}]}, "options": {}}, "name": "Set", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [450, 300], "notesInFlow": true, "notes": "generate filename", "id": "8864d17b-8310-4460-810c-d43da4e967ff"}, {"parameters": {"functionCode": "testData= 'dGVzdAo='\nif($node['Execute Command'].json['exitCode']!==0 || items[0].binary.data.data !== testData){\n  throw new Error('Error Execute Command in node');\n}\nreturn items;"}, "name": "Function", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [1050, 300], "notesInFlow": true, "notes": "verify file content", "id": "0a60312c-4030-496b-ad24-3acebff5eca1"}, {"parameters": {"command": "=rm /tmp/{{$node[\"Set\"].json[\"filename\"]}}"}, "name": "Execute Command1", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [1250, 300], "notesInFlow": true, "notes": "remove file from /tmp", "id": "ede3e595-c571-4155-85bd-14e39fe78049"}], "connections": {"Start": {"main": [[{"node": "Set", "type": "main", "index": 0}]]}, "Execute Command": {"main": [[{"node": "Read Binary File", "type": "main", "index": 0}]]}, "Set": {"main": [[{"node": "Execute Command", "type": "main", "index": 0}]]}, "Read Binary File": {"main": [[{"node": "Function", "type": "main", "index": 0}]]}, "Function": {"main": [[{"node": "Execute Command1", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}