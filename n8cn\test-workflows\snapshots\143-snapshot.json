{"data": {"startData": {}, "resultData": {"runData": {"Start": [{"hints": [], "startTime": 1738078145786, "executionTime": 0, "source": [], "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "Clearbit2": [{"hints": [], "startTime": 1738078145786, "executionTime": 523, "source": [{"previousNode": "Start"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": "f949b35a-6158-41c7-8b50-dc2c238cfbf3", "name": {"object": true}, "email": "<EMAIL>", "location": "Berlin, Berlin, DE", "timeZone": "Europe/Berlin", "utcOffset": 1, "geo": {"object": true}, "bio": {"object": true}, "site": {"object": true}, "avatar": "https://d1ts43dypk8bqh.cloudfront.net/v1/avatars/f949b35a-6158-41c7-8b50-dc2c238cfbf3", "employment": {"object": true}, "facebook": {"object": true}, "github": {"object": true}, "twitter": {"object": true}, "linkedin": {"object": true}, "googleplus": {"object": true}, "gravatar": {"object": true}, "fuzzy": false, "emailProvider": false, "indexedAt": "2025-01-16T15:13:36.503Z", "phone": {"object": true}, "inactiveAt": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "Clearbit": [{"hints": [], "startTime": 1738078146310, "executionTime": 530, "source": [{"previousNode": "Start"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": "546ba3f6-a6b7-41a1-aed8-4f9bba4119e8", "name": "n8n", "legalName": {"object": true}, "domain": "n8n.io", "domainAliases": ["json array"], "site": {"object": true}, "category": {"object": true}, "tags": ["json array"], "description": "n8n.io is a leading low-code automation tool that connects anything to everything through its open, fair code model, enabling users to build multi-step workflows with ease.", "foundedYear": 2019, "location": "Borsigstraße 27, 10115 Berlin, Germany", "timeZone": "Europe/Berlin", "utcOffset": 1, "geo": {"object": true}, "logo": "https://logo.clearbit.com/n8n.io", "facebook": {"object": true}, "linkedin": {"object": true}, "twitter": {"object": true}, "crunchbase": {"object": true}, "emailProvider": false, "type": "private", "ticker": {"object": true}, "identifiers": {"object": true}, "phone": {"object": true}, "metrics": {"object": true}, "indexedAt": "2025-01-02T01:00:13.309Z", "tech": ["json array"], "techCategories": ["json array"], "parent": {"object": true}, "ultimateParent": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "Clearbit1": [{"hints": [], "startTime": 1738078146840, "executionTime": 262, "source": [{"previousNode": "Clearbit"}], "executionStatus": "success", "data": {"main": [[{"json": {"name": "Nielsen Norman Group", "domain": "nngroup.com", "logo": "https://logo.clearbit.com/nngroup.com"}, "pairedItem": {"item": 0}}, {"json": {"name": "Новости НН.ру", "domain": "nn.ru", "logo": "https://logo.clearbit.com/nn.ru"}, "pairedItem": {"item": 0}}, {"json": {"name": "NNY360", "domain": "nny360.com", "logo": "https://logo.clearbit.com/nny360.com"}, "pairedItem": {"item": 0}}, {"json": {"name": "City of Newport News, VA - Government", "domain": "nnva.gov", "logo": "https://logo.clearbit.com/nnva.gov"}, "pairedItem": {"item": 0}}, {"json": {"name": "NNNOW", "domain": "nnnow.com", "logo": "https://logo.clearbit.com/nnnow.com"}, "pairedItem": {"item": 0}}]]}}]}, "lastNodeExecuted": "Clearbit1"}, "executionData": {"contextData": {}, "nodeExecutionStack": [], "metadata": {}, "waitingExecution": {}, "waitingExecutionSource": {}}}, "mode": "cli", "startedAt": "2025-01-28T15:29:05.785Z", "stoppedAt": "2025-01-28T15:29:07.103Z", "status": "running", "finished": true}