# 使用企业版 n8n 中文版作为基础镜像
FROM ghcr.io/deluxebear/n8n:chs

# 添加维护者信息
LABEL maintainer="ai来事 <https://www.youtube.com/@all.ai.>"

ARG CACHEBUST=666

# 设置构建参数，提供默认值
ARG WEBHOOK_URL=https://你的hf账户名-空间名.hf.space/
ARG POSTGRESDB_PORT=5432

# 切换到 root 用户进行系统配置
USER root

# 企业版特有环境变量
ENV WEBHOOK_URL=${WEBHOOK_URL} \
    N8N_HOST=0.0.0.0 \
    N8N_PORT=7860 \
    N8N_PROTOCOL=https \
    # 企业版核心配置
    N8N_ENTERPRISE_MOCK=true \
    N8N_DEFAULT_LOCALE=zh-CN \
    NODE_ENV=development \
    # 企业功能启用
    N8N_SAML_ENABLED=true \
    N8N_LDAP_ENABLED=true \
    N8N_LOG_STREAMING_ENABLED=true \
    N8N_VARIABLES_ENABLED=true \
    N8N_SOURCE_CONTROL_ENABLED=true \
    N8N_EXTERNAL_SECRETS_ENABLED=true \
    N8N_WORKFLOW_HISTORY_ENABLED=true \
    N8N_AI_ASSISTANT_ENABLED=true \
    # 基础配置
    GENERIC_TIMEZONE=Asia/Shanghai \
    N8N_METRICS=true \
    QUEUE_HEALTH_CHECK_ACTIVE=true \
    N8N_PAYLOAD_SIZE_MAX=256 \
    DB_TYPE=postgresdb \
    DB_POSTGRESDB_PORT=${POSTGRESDB_PORT} \
    # 添加超时配置
    WAIT_TIMEOUT=30 \
    # 添加日志级别
    N8N_LOG_LEVEL=info \
    # 许可证相关（企业版模拟）
    N8N_LICENSE_AUTO_RENEW_ENABLED=false \
    N8N_DIAGNOSTICS_ENABLED=false \
    N8N_VERSION_NOTIFICATIONS_ENABLED=false \
    # 修复 N8N_RELEASE_TYPE 问题
    N8N_RELEASE_TYPE=stable

# 安装必要的系统依赖（简化版）
RUN apk add --no-cache \
    bash \
    curl \
    jq \
    && mkdir -p /home/<USER>/.n8n \
    && chown -R node:node /home/<USER>/.n8n

# 创建环境变量文件
RUN --mount=type=secret,id=POSTGRES_USER,mode=0444,required=true \
    --mount=type=secret,id=POSTGRES_PASSWORD,mode=0444,required=true \
    --mount=type=secret,id=POSTGRES_DB,mode=0444,required=true \
    --mount=type=secret,id=POSTGRESDB_HOST,mode=0444,required=true \
    echo "export DB_POSTGRESDB_HOST=$(cat /run/secrets/POSTGRESDB_HOST)" >> /home/<USER>/.env && \
    echo "export DB_POSTGRESDB_USER=$(cat /run/secrets/POSTGRES_USER)" >> /home/<USER>/.env && \
    echo "export DB_POSTGRESDB_PASSWORD=$(cat /run/secrets/POSTGRES_PASSWORD)" >> /home/<USER>/.env && \
    echo "export DB_POSTGRESDB_DATABASE=$(cat /run/secrets/POSTGRES_DB)" >> /home/<USER>/.env && \
    chown node:node /home/<USER>/.env

# 创建工作目录
WORKDIR /home/<USER>/n8n

# 复制启动脚本和配置文件
COPY --chown=node:node run-minimal.sh ./run.sh
COPY --chown=node:node config/ ./config/
RUN chmod +x ./run.sh \
    && chown -R node:node /home/<USER>/n8n \
    && ls -la ./run.sh

# 暴露端口
EXPOSE 7860

# 切换到非 root 用户
USER node

# 设置数据卷
VOLUME ["/home/<USER>/.n8n"]

# 启动命令
CMD ["./run.sh"]
