{"type": "object", "properties": {"$approval": {"type": "object", "properties": {"approve": {"type": "boolean"}, "delegate": {"type": "boolean"}, "reject": {"type": "boolean"}, "resubmit": {"type": "boolean"}, "takeover": {"type": "boolean"}}}, "$approval_state": {"type": "string"}, "$approved": {"type": "boolean"}, "$currency_symbol": {"type": "string"}, "$editable": {"type": "boolean"}, "$field_states": {"type": "null"}, "$in_merge": {"type": "boolean"}, "$is_duplicate": {"type": "boolean"}, "$layout_id": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}}}, "$locked_for_me": {"type": "boolean"}, "$process_flow": {"type": "boolean"}, "$review": {"type": "null"}, "$review_process": {"type": "object", "properties": {"approve": {"type": "boolean"}, "reject": {"type": "boolean"}, "resubmit": {"type": "boolean"}}}, "$state": {"type": "string"}, "Account_Name": {"type": "string"}, "Account_Number": {"type": "string"}, "id": {"type": "string"}, "Locked__s": {"type": "boolean"}, "Owner": {"type": "object", "properties": {"email": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}}}, "Tag": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}}}}}, "version": 1}