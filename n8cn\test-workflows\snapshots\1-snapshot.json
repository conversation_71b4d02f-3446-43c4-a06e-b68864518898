{"data": {"startData": {}, "resultData": {"runData": {"Start": [{"startTime": 1676891385621, "executionTime": 1, "source": [], "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "Twitter": [{"startTime": 1676891385623, "executionTime": 416, "source": [{"previousNode": "Start"}], "executionStatus": "success", "data": {"main": [[{"json": {"created_at": "Mon Feb 20 11:09:45 +0000 2023", "id": 1627626558218182700, "id_str": "1627626558218182657", "text": "Hello from n8n testing framework 0.5036048381984097", "truncated": false, "entities": {"object": true}, "source": "<a href=\"https://empty-quail-11.hooks.n8n.cloud/\" rel=\"nofollow\">n8n testing framework</a>", "in_reply_to_status_id": {"object": true}, "in_reply_to_status_id_str": {"object": true}, "in_reply_to_user_id": {"object": true}, "in_reply_to_user_id_str": {"object": true}, "in_reply_to_screen_name": {"object": true}, "user": {"object": true}, "geo": {"object": true}, "coordinates": {"object": true}, "place": {"object": true}, "contributors": {"object": true}, "is_quote_status": false, "retweet_count": 0, "favorite_count": 0, "favorited": false, "retweeted": false, "lang": "en"}, "pairedItem": {"item": 0}}]]}}], "Twitter4": [{"startTime": 1676891386039, "executionTime": 227, "source": [{"previousNode": "Start"}], "executionStatus": "success", "data": {"main": [[{"json": {"created_at": "Sun Feb 19 15:58:36 +0000 2023", "id": 1627336861717876700, "id_str": "1627336861717876742", "text": "- @PlausibleHQ tracking plan, in progress 🚧\n- @n8n_io workflows✅\n- @MailerLite add Users subscription✅\n- @NotionHQ… https://t.co/2Mb94nO48X", "truncated": true, "entities": {"object": true}, "metadata": {"object": true}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "in_reply_to_status_id": {"object": true}, "in_reply_to_status_id_str": {"object": true}, "in_reply_to_user_id": {"object": true}, "in_reply_to_user_id_str": {"object": true}, "in_reply_to_screen_name": {"object": true}, "user": {"object": true}, "geo": {"object": true}, "coordinates": {"object": true}, "place": {"object": true}, "contributors": {"object": true}, "is_quote_status": true, "retweet_count": 0, "favorite_count": 4, "favorited": false, "retweeted": false, "lang": "en"}, "pairedItem": {"item": 0}}]]}}], "Twitter1": [{"startTime": 1676891386266, "executionTime": 234, "source": [{"previousNode": "Twitter"}], "executionStatus": "success", "data": {"main": [[{"json": {"created_at": "Mon Feb 20 11:09:45 +0000 2023", "id": 1627626558218182700, "id_str": "1627626558218182657", "text": "Hello from n8n testing framework 0.5036048381984097", "truncated": false, "entities": {"object": true}, "source": "<a href=\"https://empty-quail-11.hooks.n8n.cloud/\" rel=\"nofollow\">n8n testing framework</a>", "in_reply_to_status_id": {"object": true}, "in_reply_to_status_id_str": {"object": true}, "in_reply_to_user_id": {"object": true}, "in_reply_to_user_id_str": {"object": true}, "in_reply_to_screen_name": {"object": true}, "user": {"object": true}, "geo": {"object": true}, "coordinates": {"object": true}, "place": {"object": true}, "contributors": {"object": true}, "is_quote_status": false, "retweet_count": 0, "favorite_count": 0, "favorited": true, "retweeted": false, "lang": "en"}, "pairedItem": {"item": 0}}]]}}], "Twitter2": [{"startTime": 1676891386500, "executionTime": 244, "source": [{"previousNode": "Twitter1"}], "executionStatus": "success", "data": {"main": [[{"json": {"created_at": "Mon Feb 20 11:09:46 +0000 2023", "id": 1627626561162682400, "id_str": "1627626561162682372", "text": "RT @nodeqa1: Hello from n8n testing framework 0.5036048381984097", "truncated": false, "entities": {"object": true}, "source": "<a href=\"https://empty-quail-11.hooks.n8n.cloud/\" rel=\"nofollow\">n8n testing framework</a>", "in_reply_to_status_id": {"object": true}, "in_reply_to_status_id_str": {"object": true}, "in_reply_to_user_id": {"object": true}, "in_reply_to_user_id_str": {"object": true}, "in_reply_to_screen_name": {"object": true}, "user": {"object": true}, "geo": {"object": true}, "coordinates": {"object": true}, "place": {"object": true}, "contributors": {"object": true}, "retweeted_status": {"object": true}, "is_quote_status": false, "retweet_count": 1, "favorite_count": 0, "favorited": true, "retweeted": true, "lang": "en"}, "pairedItem": {"item": 0}}]]}}], "Twitter3": [{"startTime": 1676891386744, "executionTime": 218, "source": [{"previousNode": "Twitter2"}], "executionStatus": "success", "data": {"main": [[{"json": {"created_at": "Mon Feb 20 11:09:45 +0000 2023", "id": 1627626558218182700, "id_str": "1627626558218182657", "text": "Hello from n8n testing framework 0.5036048381984097", "truncated": false, "entities": {"object": true}, "source": "<a href=\"https://empty-quail-11.hooks.n8n.cloud/\" rel=\"nofollow\">n8n testing framework</a>", "in_reply_to_status_id": {"object": true}, "in_reply_to_status_id_str": {"object": true}, "in_reply_to_user_id": {"object": true}, "in_reply_to_user_id_str": {"object": true}, "in_reply_to_screen_name": {"object": true}, "user": {"object": true}, "geo": {"object": true}, "coordinates": {"object": true}, "place": {"object": true}, "contributors": {"object": true}, "is_quote_status": false, "retweet_count": 1, "favorite_count": 1, "favorited": true, "retweeted": true, "lang": "en"}, "pairedItem": {"item": 0}}]]}}]}, "lastNodeExecuted": "Twitter3"}, "executionData": {"contextData": {}, "nodeExecutionStack": [], "waitingExecution": {}, "waitingExecutionSource": {}}}, "mode": "cli", "startedAt": "2023-02-20T11:09:45.616Z", "stoppedAt": "2023-02-20T11:09:46.962Z", "status": "running", "finished": true}