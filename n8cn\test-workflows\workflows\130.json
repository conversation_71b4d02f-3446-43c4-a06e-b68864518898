{"createdAt": "2021-03-12T12:14:08.727Z", "updatedAt": "2021-03-12T12:35:37.990Z", "id": "130", "name": "Wekan:Board:create get getAll delete:List:create get getAll delete:Card:create update get getAll delete:CardComment:create get getAll delete:CheckList:create get getAll delete:CheckListItem:get update delete", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300], "id": "d1be5d3e-3071-482a-a8d0-6cd3e1784efd"}, {"parameters": {"resource": "board", "title": "=Board{{Date.now()}}", "owner": "E27bvzwJ5A26xfAPG", "additionalFields": {}}, "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.wekan", "typeVersion": 1, "position": [490, 280], "credentials": {"wekanApi": {"id": "97", "name": "Wekan API creds"}}, "id": "1013b4a2-232f-417c-ad61-c424a10eee2a"}, {"parameters": {"resource": "board", "operation": "get", "boardId": "={{$node[\"Wekan\"].json[\"_id\"]}}"}, "name": "Wekan1", "type": "n8n-nodes-base.wekan", "typeVersion": 1, "position": [650, 280], "credentials": {"wekanApi": {"id": "97", "name": "Wekan API creds"}}, "id": "0bd0df50-577c-4f42-b653-a933059d5d94"}, {"parameters": {"resource": "board", "operation": "getAll", "IdUser": "E27bvzwJ5A26xfAPG", "limit": 1}, "name": "Wekan2", "type": "n8n-nodes-base.wekan", "typeVersion": 1, "position": [800, 280], "credentials": {"wekanApi": {"id": "97", "name": "Wekan API creds"}}, "id": "189c8afb-3890-4755-aec7-17577b14f6e7"}, {"parameters": {"resource": "board", "operation": "delete", "boardId": "={{$node[\"Wekan\"].json[\"_id\"]}}"}, "name": "Wekan3", "type": "n8n-nodes-base.wekan", "typeVersion": 1, "position": [3500, 260], "credentials": {"wekanApi": {"id": "97", "name": "Wekan API creds"}}, "id": "302a053a-5456-46c0-b6bf-47e3fad8d061"}, {"parameters": {"resource": "list", "boardId": "={{$node[\"Wekan\"].json[\"_id\"]}}", "title": "=List{{Date.now()}}"}, "name": "Wekan4", "type": "n8n-nodes-base.wekan", "typeVersion": 1, "position": [950, 400], "credentials": {"wekanApi": {"id": "97", "name": "Wekan API creds"}}, "id": "901bd245-0434-43a4-9c6e-2e0d0012f25b"}, {"parameters": {"resource": "list", "operation": "get", "boardId": "={{$node[\"Wekan\"].json[\"_id\"]}}", "listId": "={{$node[\"Wekan4\"].json[\"_id\"]}}"}, "name": "Wekan5", "type": "n8n-nodes-base.wekan", "typeVersion": 1, "position": [1100, 400], "credentials": {"wekanApi": {"id": "97", "name": "Wekan API creds"}}, "id": "1ad2c40f-606f-468e-b304-f67ba7f09857"}, {"parameters": {"resource": "list", "operation": "getAll", "boardId": "={{$node[\"Wekan\"].json[\"_id\"]}}", "limit": 1}, "name": "Wekan6", "type": "n8n-nodes-base.wekan", "typeVersion": 1, "position": [1250, 400], "credentials": {"wekanApi": {"id": "97", "name": "Wekan API creds"}}, "id": "7e0bf795-d18a-44fc-b1d0-07dc726d1b9a"}, {"parameters": {"resource": "list", "operation": "delete", "boardId": "={{$node[\"Wekan\"].json[\"_id\"]}}", "listId": "={{$node[\"Wekan4\"].json[\"_id\"]}}"}, "name": "Wekan7", "type": "n8n-nodes-base.wekan", "typeVersion": 1, "position": [3350, 400], "credentials": {"wekanApi": {"id": "97", "name": "Wekan API creds"}}, "id": "4dff32d6-2cfd-4cf7-a5f4-cb670c8f5283"}, {"parameters": {"boardId": "={{$node[\"Wekan\"].json[\"_id\"]}}", "listId": "={{$node[\"Wekan4\"].json[\"_id\"]}}", "title": "=Card{{Date.now()}}", "swimlaneId": "CJ44cd7gPRf8qT3Xb", "authorId": "E27bvzwJ5A26xfAPG", "additionalFields": {}}, "name": "Wekan8", "type": "n8n-nodes-base.wekan", "typeVersion": 1, "position": [1400, 500], "credentials": {"wekanApi": {"id": "97", "name": "Wekan API creds"}}, "id": "03ad5f1f-23df-431c-a98f-3fd966dcd4fa"}, {"parameters": {"operation": "update", "boardId": "={{$node[\"Wekan\"].json[\"_id\"]}}", "listId": "={{$node[\"Wekan4\"].json[\"_id\"]}}", "cardId": "={{$node[\"Wekan8\"].json[\"_id\"]}}", "updateFields": {"title": "=UpdatedCard{{Date.now()}}"}}, "name": "Wekan9", "type": "n8n-nodes-base.wekan", "typeVersion": 1, "position": [1550, 500], "credentials": {"wekanApi": {"id": "97", "name": "Wekan API creds"}}, "id": "c0297bac-351b-4af3-ad47-1e4a3f504828"}, {"parameters": {"operation": "get", "boardId": "={{$node[\"Wekan\"].json[\"_id\"]}}", "listId": "={{$node[\"Wekan4\"].json[\"_id\"]}}", "cardId": "={{$node[\"Wekan8\"].json[\"_id\"]}}"}, "name": "Wekan10", "type": "n8n-nodes-base.wekan", "typeVersion": 1, "position": [1700, 500], "credentials": {"wekanApi": {"id": "97", "name": "Wekan API creds"}}, "id": "fd68635a-fbf9-4d53-a835-1dc9be3b6fa6"}, {"parameters": {"operation": "getAll", "boardId": "={{$node[\"Wekan\"].json[\"_id\"]}}", "fromObject": "list", "listId": "={{$node[\"Wekan4\"].json[\"_id\"]}}", "limit": 1}, "name": "Wekan11", "type": "n8n-nodes-base.wekan", "typeVersion": 1, "position": [1850, 500], "credentials": {"wekanApi": {"id": "97", "name": "Wekan API creds"}}, "id": "da544079-7689-423e-8990-3b886d44d040"}, {"parameters": {"operation": "delete", "boardId": "={{$node[\"Wekan\"].json[\"_id\"]}}", "listId": "={{$node[\"Wekan4\"].json[\"_id\"]}}", "cardId": "={{$node[\"Wekan8\"].json[\"_id\"]}}"}, "name": "Wekan12", "type": "n8n-nodes-base.wekan", "typeVersion": 1, "position": [3220, 500], "credentials": {"wekanApi": {"id": "97", "name": "Wekan API creds"}}, "id": "b33dde7e-b503-480b-9dc5-3cf6b68d3b02"}, {"parameters": {"resource": "cardComment", "boardId": "={{$node[\"Wekan\"].json[\"_id\"]}}", "listId": "={{$node[\"Wekan4\"].json[\"_id\"]}}", "cardId": "={{$node[\"Wekan8\"].json[\"_id\"]}}", "authorId": "E27bvzwJ5A26xfAPG", "comment": "=CardComment{{Date.now()}}"}, "name": "Wekan13", "type": "n8n-nodes-base.wekan", "typeVersion": 1, "position": [2000, 650], "credentials": {"wekanApi": {"id": "97", "name": "Wekan API creds"}}, "id": "56c3500d-d9d3-49db-a6cd-fe4558c901df"}, {"parameters": {"resource": "cardComment", "operation": "get", "boardId": "={{$node[\"Wekan\"].json[\"_id\"]}}", "listId": "={{$node[\"Wekan4\"].json[\"_id\"]}}", "cardId": "={{$node[\"Wekan8\"].json[\"_id\"]}}", "commentId": "={{$node[\"Wekan13\"].json[\"_id\"]}}"}, "name": "Wekan14", "type": "n8n-nodes-base.wekan", "typeVersion": 1, "position": [2150, 650], "credentials": {"wekanApi": {"id": "97", "name": "Wekan API creds"}}, "id": "995809d9-3fb6-4968-9590-c4b929e83352"}, {"parameters": {"resource": "cardComment", "operation": "getAll", "boardId": "={{$node[\"Wekan\"].json[\"_id\"]}}", "listId": "={{$node[\"Wekan4\"].json[\"_id\"]}}", "cardId": "={{$node[\"Wekan8\"].json[\"_id\"]}}", "limit": 1}, "name": "Wekan15", "type": "n8n-nodes-base.wekan", "typeVersion": 1, "position": [2300, 650], "credentials": {"wekanApi": {"id": "97", "name": "Wekan API creds"}}, "id": "1d3d120f-17eb-4f17-9513-cf99d2bd5068"}, {"parameters": {"resource": "cardComment", "operation": "delete", "boardId": "={{$node[\"Wekan\"].json[\"_id\"]}}", "listId": "={{$node[\"Wekan4\"].json[\"_id\"]}}", "cardId": "={{$node[\"Wekan8\"].json[\"_id\"]}}", "commentId": "={{$node[\"Wekan13\"].json[\"_id\"]}}"}, "name": "Wekan16", "type": "n8n-nodes-base.wekan", "typeVersion": 1, "position": [2450, 650], "credentials": {"wekanApi": {"id": "97", "name": "Wekan API creds"}}, "id": "d0960908-6aaf-4284-a2a3-0c2bb305afd7"}, {"parameters": {"resource": "checklist", "operation": "create", "boardId": "={{$node[\"Wekan\"].json[\"_id\"]}}", "listId": "={{$node[\"Wekan4\"].json[\"_id\"]}}", "cardId": "={{$node[\"Wekan8\"].json[\"_id\"]}}", "title": "=Checklist{{Date.now()}}", "items": ["=ChecklistItem{{Date.now()}}"]}, "name": "Wekan17", "type": "n8n-nodes-base.wekan", "typeVersion": 1, "position": [2000, 350], "credentials": {"wekanApi": {"id": "97", "name": "Wekan API creds"}}, "id": "93b5ad63-5527-49b2-8803-d91136052169"}, {"parameters": {"resource": "checklist", "operation": "get", "boardId": "={{$node[\"Wekan\"].json[\"_id\"]}}", "listId": "={{$node[\"Wekan4\"].json[\"_id\"]}}", "cardId": "={{$node[\"Wekan8\"].json[\"_id\"]}}", "checklistId": "={{$node[\"Wekan17\"].json[\"_id\"]}}"}, "name": "Wekan18", "type": "n8n-nodes-base.wekan", "typeVersion": 1, "position": [2150, 350], "credentials": {"wekanApi": {"id": "97", "name": "Wekan API creds"}}, "id": "cf310149-4468-4109-9dad-620030f49c4f"}, {"parameters": {"resource": "checklist", "boardId": "={{$node[\"Wekan\"].json[\"_id\"]}}", "listId": "={{$node[\"Wekan4\"].json[\"_id\"]}}", "cardId": "={{$node[\"Wekan8\"].json[\"_id\"]}}", "limit": 1}, "name": "Wekan19", "type": "n8n-nodes-base.wekan", "typeVersion": 1, "position": [2300, 350], "credentials": {"wekanApi": {"id": "97", "name": "Wekan API creds"}}, "id": "34c95cdf-0dd2-43d2-8635-e5cc36fcb6d8"}, {"parameters": {"resource": "checklist", "operation": "delete", "boardId": "={{$node[\"Wekan\"].json[\"_id\"]}}", "listId": "={{$node[\"Wekan4\"].json[\"_id\"]}}", "cardId": "={{$node[\"Wekan8\"].json[\"_id\"]}}", "checklistId": "={{$node[\"Wekan17\"].json[\"_id\"]}}"}, "name": "Wekan20", "type": "n8n-nodes-base.wekan", "typeVersion": 1, "position": [2900, 350], "credentials": {"wekanApi": {"id": "97", "name": "Wekan API creds"}}, "id": "e8a93285-5090-4dff-81d6-c6dea77512af"}, {"parameters": {"resource": "checklistItem", "operation": "get", "boardId": "={{$node[\"Wekan\"].json[\"_id\"]}}", "listId": "={{$node[\"Wekan4\"].json[\"_id\"]}}", "cardId": "={{$node[\"Wekan8\"].json[\"_id\"]}}", "checklistId": "={{$node[\"Wekan17\"].json[\"_id\"]}}", "checklistItemId": "={{$node[\"Wekan18\"].json[\"items\"][0][\"_id\"]}}"}, "name": "Wekan21", "type": "n8n-nodes-base.wekan", "typeVersion": 1, "position": [2450, 450], "credentials": {"wekanApi": {"id": "97", "name": "Wekan API creds"}}, "id": "8059fe8f-a56d-4b12-9129-6bca650a56f5"}, {"parameters": {"resource": "checklistItem", "operation": "update", "boardId": "={{$node[\"Wekan\"].json[\"_id\"]}}", "listId": "={{$node[\"Wekan4\"].json[\"_id\"]}}", "cardId": "={{$node[\"Wekan8\"].json[\"_id\"]}}", "checklistId": "={{$node[\"Wekan17\"].json[\"_id\"]}}", "checklistItemId": "={{$node[\"Wekan18\"].json[\"items\"][0][\"_id\"]}}", "updateFields": {"isFinished": true}}, "name": "Wekan22", "type": "n8n-nodes-base.wekan", "typeVersion": 1, "position": [2600, 450], "credentials": {"wekanApi": {"id": "97", "name": "Wekan API creds"}}, "id": "02ca495d-50c2-4b96-b66a-287f630c95c5"}, {"parameters": {"resource": "checklistItem", "operation": "delete", "boardId": "={{$node[\"Wekan\"].json[\"_id\"]}}", "listId": "={{$node[\"Wekan4\"].json[\"_id\"]}}", "cardId": "={{$node[\"Wekan8\"].json[\"_id\"]}}", "checklistId": "={{$node[\"Wekan17\"].json[\"_id\"]}}", "checklistItemId": "={{$node[\"Wekan18\"].json[\"items\"][0][\"_id\"]}}"}, "name": "Wekan23", "type": "n8n-nodes-base.wekan", "typeVersion": 1, "position": [2750, 450], "credentials": {"wekanApi": {"id": "97", "name": "Wekan API creds"}}, "id": "0e35867f-089c-4a95-985a-1aeafe45cd6e"}, {"parameters": {"mode": "wait"}, "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "typeVersion": 1, "position": [3050, 500], "id": "a72e478c-ae36-4775-9568-494f34ea4edc"}], "connections": {"Start": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Wekan": {"main": [[{"node": "Wekan1", "type": "main", "index": 0}]]}, "Wekan1": {"main": [[{"node": "Wekan2", "type": "main", "index": 0}]]}, "Wekan2": {"main": [[{"node": "Wekan4", "type": "main", "index": 0}]]}, "Wekan4": {"main": [[{"node": "Wekan5", "type": "main", "index": 0}]]}, "Wekan5": {"main": [[{"node": "Wekan6", "type": "main", "index": 0}]]}, "Wekan6": {"main": [[{"node": "Wekan8", "type": "main", "index": 0}]]}, "Wekan7": {"main": [[{"node": "Wekan3", "type": "main", "index": 0}]]}, "Wekan8": {"main": [[{"node": "Wekan9", "type": "main", "index": 0}]]}, "Wekan9": {"main": [[{"node": "Wekan10", "type": "main", "index": 0}]]}, "Wekan10": {"main": [[{"node": "Wekan11", "type": "main", "index": 0}]]}, "Wekan12": {"main": [[{"node": "Wekan7", "type": "main", "index": 0}]]}, "Wekan11": {"main": [[{"node": "Wekan13", "type": "main", "index": 0}, {"node": "Wekan17", "type": "main", "index": 0}]]}, "Wekan13": {"main": [[{"node": "Wekan14", "type": "main", "index": 0}]]}, "Wekan14": {"main": [[{"node": "Wekan15", "type": "main", "index": 0}]]}, "Wekan16": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Wekan17": {"main": [[{"node": "Wekan18", "type": "main", "index": 0}]]}, "Wekan18": {"main": [[{"node": "Wekan19", "type": "main", "index": 0}]]}, "Wekan20": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Wekan15": {"main": [[{"node": "Wekan16", "type": "main", "index": 0}]]}, "Wekan19": {"main": [[{"node": "Wekan21", "type": "main", "index": 0}]]}, "Wekan21": {"main": [[{"node": "Wekan22", "type": "main", "index": 0}]]}, "Wekan22": {"main": [[{"node": "Wekan23", "type": "main", "index": 0}]]}, "Wekan23": {"main": [[{"node": "Wekan20", "type": "main", "index": 0}]]}, "Merge": {"main": [[{"node": "Wekan12", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}