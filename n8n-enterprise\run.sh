#!/bin/bash
set -eo pipefail

# 导入环境变量
source /home/<USER>/.env

# 错误处理函数
handle_error() {
    echo "错误发生在第 $1 行"
    exit 1
}
trap 'handle_error $LINENO' ERR

# 超时处理函数
timeout_handler() {
    echo "操作超时"
    exit 1
}

# 等待服务就绪的通用函数
wait_for_service() {
    local service=$1
    local host=$2
    local port=$3
    local timeout=${4:-$WAIT_TIMEOUT}

    echo "等待 $service 就绪..."
    local end=$((SECONDS + timeout))

    while [ $SECONDS -lt $end ]; do
        if nc -z -w1 "$host" "$port" >/dev/null 2>&1; then
            echo "$service 已就绪"
            return 0
        fi
        echo "尝试连接 $service at $host:$port..."
        sleep 1
    done

    echo "$service 启动超时"
    exit 1
}

# 启动 Redis 服务
start_redis() {
    echo "Starting Redis server..."

    # 在 Alpine Linux 中启动 Redis
    redis-server --daemonize yes --bind 127.0.0.1 --port 6379 --maxmemory 512mb --maxmemory-policy allkeys-lru
    sleep 2

    # 检查 Redis 是否启动成功
    local timeout=10
    local end=$((SECONDS + timeout))

    while [ $SECONDS -lt $end ]; do
        if redis-cli ping > /dev/null 2>&1; then
            echo "Redis server started successfully"
            echo ""
            return 0
        fi
        echo "Waiting for Redis to start..."
        sleep 1
    done

    echo "Failed to start Redis server"
    exit 1
}

# 启动 Qdrant 服务
start_qdrant() {
    echo "Starting Qdrant server..."

    # 确保目录存在并有正确的权限
    mkdir -p /home/<USER>/.n8n/qdrant/storage
    mkdir -p /home/<USER>/.n8n/qdrant/config
    mkdir -p /home/<USER>/.n8n/qdrant/snapshots
    mkdir -p /home/<USER>/.n8n/qdrant/logs

    # 设置正确的权限
    chmod -R 755 /home/<USER>/.n8n/qdrant
    chown -R node:node /home/<USER>/.n8n/qdrant

    # 创建 Qdrant 配置文件
    cat > /home/<USER>/.n8n/qdrant/config/config.yaml <<EOF
service:
  host: 0.0.0.0
  http_port: 6333
  grpc_port: 6334
  enable_cors: true
  enable_tls: false
  max_request_size_mb: 64
  max_workers: 0
storage:
  storage_path: /home/<USER>/.n8n/qdrant/storage
  snapshots_path: /home/<USER>/.n8n/qdrant/snapshots
  on_disk_payload: true

  performance:
    max_search_threads: 0
    max_optimization_threads: 0

  optimizers:
    deleted_threshold: 0.2
    vacuum_min_vector_number: 1000
    default_segment_number: 0
    max_segment_size_kb: null
    indexing_threshold_kb: 20000
    flush_interval_sec: 5

  hnsw_index:
    m: 16
    ef_construct: 100
    full_scan_threshold_kb: 10000
    max_indexing_threads: 0
    on_disk: false
logger:
  on_disk:
    enabled: true
    log_file: /home/<USER>/.n8n/qdrant/logs/qdrant.log
    log_level: INFO
telemetry_disabled: true
EOF

    # 确保配置文件有正确的权限
    chmod 644 /home/<USER>/.n8n/qdrant/config/config.yaml

    # 使用配置文件启动 Qdrant
    qdrant --config-path /home/<USER>/.n8n/qdrant/config/config.yaml > /home/<USER>/.n8n/qdrant/logs/startup.log 2>&1 &

    # 等待 Qdrant 启动
    local timeout=30
    local end=$((SECONDS + timeout))

    while [ $SECONDS -lt $end ]; do
        if curl -s http://localhost:6333/health >/dev/null; then
            echo "Qdrant server started successfully"

            # 预创建常用集合
            echo "Creating default collections..."

            # 创建文本向量集合 (768维，适用于多数文本嵌入模型)
            curl -X PUT 'http://localhost:6333/collections/text_vectors' \
                -H 'Content-Type: application/json' \
                -d '{
                    "vectors": {
                        "size": 768,
                        "distance": "Cosine",
                        "on_disk": true
                    },
                    "optimizers_config": {
                        "default_segment_number": 2,
                        "indexing_threshold": 20000,
                        "memmap_threshold": 10000
                    },
                    "hnsw_config": {
                        "m": 16,
                        "ef_construct": 100,
                        "full_scan_threshold": 10000,
                        "max_indexing_threads": 0,
                        "on_disk": true
                    }
                }' >/dev/null 2>&1

            # 创建 OpenAI 向量集合 (1536维)
            curl -X PUT 'http://localhost:6333/collections/openai_vectors' \
                -H 'Content-Type: application/json' \
                -d '{
                    "vectors": {
                        "size": 1536,
                        "distance": "Cosine"
                    },
                    "optimizers_config": {
                        "default_segment_number": 2,
                        "indexing_threshold": 20000
                    },
                    "hnsw_config": {
                        "m": 16,
                        "ef_construct": 100,
                        "full_scan_threshold": 10000
                    }
                }' >/dev/null 2>&1

            # 验证集合创建状态
            echo -e "\nVerifying collections:"
            curl -s 'http://localhost:6333/collections' | jq '.' || echo "Collections created successfully"

            return 0
        fi
        echo "Waiting for Qdrant to start..."
        sleep 1

        # 检查是否有错误日志
        if grep -i "error" /home/<USER>/.n8n/qdrant/logs/startup.log >/dev/null 2>&1; then
            echo "Error found in Qdrant logs:"
            tail -n 10 /home/<USER>/.n8n/qdrant/logs/startup.log
        fi
    done

    echo "Failed to start Qdrant server"
    echo "Last 10 lines of Qdrant log:"
    tail -n 10 /home/<USER>/.n8n/qdrant/logs/startup.log
    exit 1
}

# 检查企业版功能状态
check_enterprise_features() {
    echo "🚀 N8N ENTERPRISE MOCK ENABLED"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo "All enterprise features are unlocked for testing!"
    echo ""
    echo "Enterprise Features Available:"
    echo "✅ SAML单点登录"
    echo "✅ LDAP/Active Directory集成"
    echo "✅ 高级权限管理"
    echo "✅ 工作流和凭证共享"
    echo "✅ 项目管理"
    echo "✅ Git源代码控制"
    echo "✅ 环境变量管理"
    echo "✅ 外部密钥管理"
    echo "✅ 工作流版本历史"
    echo "✅ AI助手功能"
    echo "✅ 多主实例高可用"
    echo "✅ 日志流式传输"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo ""
}

# 检查服务状态
check_services() {
    echo "检查服务状态..."

    # 检查 Redis
    echo "Redis 状态："
    redis-cli info | grep 'used_memory\|connected_clients\|total_connections_received'

    # 检查 Qdrant
    echo "Qdrant 状态："
    if curl -s http://localhost:6333/metrics >/dev/null; then
        echo "Qdrant 运行正常"

        # 显示集合信息
        echo "Qdrant 集合列表："
        curl -s http://localhost:6333/collections | jq '.' || echo "Collections available"
    else
        echo "Qdrant 服务异常"
        tail -n 10 /home/<USER>/.n8n/qdrant/logs/qdrant.log
    fi
}

# 主流程
main() {
    current_time=$(date +"%Y-%m-%d %H:%M:%S")
    echo "Starting N8N Enterprise Edition at $current_time"

    # 显示企业版功能状态
    check_enterprise_features

    # 输出配置信息
    echo "Database Configuration:"
    echo "Host: ${DB_POSTGRESDB_HOST}"
    echo "Port: ${DB_POSTGRESDB_PORT}"
    echo "User: ${DB_POSTGRESDB_USER}"
    echo "Database: ${DB_POSTGRESDB_DATABASE}"
    echo "Type: ${DB_TYPE}"
    echo ""

    # 启动服务
    wait_for_service "PostgreSQL" "${DB_POSTGRESDB_HOST}" "${DB_POSTGRESDB_PORT}"
    echo ""
    start_redis
    echo ""
    start_qdrant
    echo ""
    check_services

    # 设置 N8N 环境变量（如果配置文件存在）
    if [ -f "/home/<USER>/n8n/config/n8n_env.sh" ]; then
        source /home/<USER>/n8n/config/n8n_env.sh
    fi

    echo ""
    echo "Starting n8n Enterprise Edition..."
    echo "Access your n8n instance at: ${WEBHOOK_URL}"
    echo ""
    exec n8n start
}

# 执行主流程
main "$@"