{"createdAt": "2021-03-19T10:44:59.685Z", "updatedAt": "2021-05-21T10:01:36.962Z", "id": "141", "name": "MicrosoftExcel:Table:lookup getRows getColumns addRow:Workbook:addWorksheet getAll:Worksheet:getAll getContent", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300], "id": "13273e42-d319-4d4b-8a72-4c034ed61bac"}, {"parameters": {"operation": "addWorksheet", "workbook": "51F07A8CA9E72D69!136", "additionalFields": {"name": "=WorkSheet{{Date.now()}}"}}, "name": "Microsoft Excel", "type": "n8n-nodes-base.microsoftExcel", "typeVersion": 1, "position": [500, 300], "credentials": {"microsoftExcelOAuth2Api": {"id": "73", "name": "Microsoft Excel OAuth2 creds"}}, "id": "b95b3ba8-c4c8-48ff-9458-3ffdf1445f25"}, {"parameters": {"operation": "getAll", "limit": 1, "filters": {}}, "name": "Microsoft Excel1", "type": "n8n-nodes-base.microsoftExcel", "typeVersion": 1, "position": [790, 300], "credentials": {"microsoftExcelOAuth2Api": {"id": "73", "name": "Microsoft Excel OAuth2 creds"}}, "id": "e8bcc5f5-7348-466f-92cd-52b12f0aff5e"}, {"parameters": {"resource": "worksheet", "operation": "getAll", "workbook": "51F07A8CA9E72D69!136", "limit": 1, "filters": {}}, "name": "Microsoft Excel2", "type": "n8n-nodes-base.microsoftExcel", "typeVersion": 1, "position": [500, 450], "alwaysOutputData": true, "credentials": {"microsoftExcelOAuth2Api": {"id": "73", "name": "Microsoft Excel OAuth2 creds"}}, "id": "859d409b-f7c9-47e1-af7d-e1555c2745a9"}, {"parameters": {"resource": "worksheet", "operation": "get<PERSON>ontent", "workbook": "51F07A8CA9E72D69!136", "worksheet": "{00000000-0001-0000-0000-000000000000}", "range": "A1:D2"}, "name": "Microsoft Excel3", "type": "n8n-nodes-base.microsoftExcel", "typeVersion": 1, "position": [790, 450], "credentials": {"microsoftExcelOAuth2Api": {"id": "73", "name": "Microsoft Excel OAuth2 creds"}}, "id": "b9f51de5-c632-49bb-815a-f7fe13e8cce6"}, {"parameters": {"resource": "table", "operation": "lookup", "workbook": "51F07A8CA9E72D69!136", "worksheet": "{00000000-0001-0000-0000-000000000000}", "table": "{36FB1939-0994-4A3D-8612-14A5283A6A80}", "lookupColumn": "t", "lookupValue": "n", "options": {}}, "name": "Microsoft Excel4", "type": "n8n-nodes-base.microsoftExcel", "typeVersion": 1, "position": [500, 150], "credentials": {"microsoftExcelOAuth2Api": {"id": "73", "name": "Microsoft Excel OAuth2 creds"}}, "id": "fbc761ca-7add-4e72-942d-4a55366491fc"}, {"parameters": {"resource": "table", "operation": "getRows", "workbook": "51F07A8CA9E72D69!136", "worksheet": "{00000000-0001-0000-0000-000000000000}", "table": "{36FB1939-0994-4A3D-8612-14A5283A6A80}", "limit": 1}, "name": "Microsoft Excel5", "type": "n8n-nodes-base.microsoftExcel", "typeVersion": 1, "position": [800, 150], "credentials": {"microsoftExcelOAuth2Api": {"id": "73", "name": "Microsoft Excel OAuth2 creds"}}, "id": "e0c06d78-2108-457e-955c-7abaf4b377dd"}, {"parameters": {"resource": "table", "operation": "getColumns", "workbook": "51F07A8CA9E72D69!136", "worksheet": "{00000000-0001-0000-0000-000000000000}", "table": "{36FB1939-0994-4A3D-8612-14A5283A6A80}", "limit": 1}, "name": "Microsoft Excel6", "type": "n8n-nodes-base.microsoftExcel", "typeVersion": 1, "position": [1100, 150], "credentials": {"microsoftExcelOAuth2Api": {"id": "73", "name": "Microsoft Excel OAuth2 creds"}}, "id": "433e7bea-93de-4efc-b77b-4d039e685527"}, {"parameters": {"resource": "table", "workbook": "51F07A8CA9E72D69!136", "worksheet": "{00000000-0001-0000-0000-000000000000}", "table": "{36FB1939-0994-4A3D-8612-14A5283A6A80}", "additionalFields": {}}, "name": "Microsoft Excel7", "type": "n8n-nodes-base.microsoftExcel", "typeVersion": 1, "position": [1400, 150], "credentials": {"microsoftExcelOAuth2Api": {"id": "73", "name": "Microsoft Excel OAuth2 creds"}}, "id": "5ea1e83f-a0fb-4c12-b722-63b8419fc78a"}, {"parameters": {"keepOnlySet": true, "values": {"string": [{"name": "Column3", "value": "={{Date.now().toString().substr(6)}}"}, {"name": "Column4", "value": "={{Date.now().toString().substr(3)}}"}], "number": [{"name": "t", "value": "={{Math.round(Math.random()*100)}}"}, {"name": "e", "value": "={{Math.round(Math.random()*100)}}"}, {"name": "s", "value": "={{Math.round(Math.random()*100)}}"}, {"name": "t2", "value": "={{Math.round(Math.random()*100)}}"}]}, "options": {}}, "name": "Set", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [1250, 150], "id": "eb3f568f-c6f6-4a53-b6de-8de28f762848"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(800);\n\n// Output data\nreturn items;"}, "name": "Sleep 0.8 second", "type": "n8n-nodes-base.function", "position": [650, 450], "typeVersion": 1, "id": "5a289dee-eba5-467f-a7b3-552d94867c0f"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(800);\n\n// Output data\nreturn items;"}, "name": "Sleep 0.8 second1", "type": "n8n-nodes-base.function", "position": [650, 300], "typeVersion": 1, "id": "0ca0ff2b-609a-42a3-a1cf-2bbc8f127f01"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(800);\n\n// Output data\nreturn items;"}, "name": "Sleep 0.8 second2", "type": "n8n-nodes-base.function", "position": [650, 150], "typeVersion": 1, "id": "0bb04e5a-6001-4bc1-a1dd-32b16f3f0ec0"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(800);\n\n// Output data\nreturn items;"}, "name": "Sleep 0.8 second3", "type": "n8n-nodes-base.function", "position": [950, 150], "typeVersion": 1, "id": "9704ee84-3b4f-419c-aaa3-3e58c16b7ca9"}], "connections": {"Microsoft Excel": {"main": [[{"node": "Sleep 0.8 second1", "type": "main", "index": 0}]]}, "Microsoft Excel2": {"main": [[{"node": "Sleep 0.8 second", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "Microsoft Excel", "type": "main", "index": 0}, {"node": "Microsoft Excel2", "type": "main", "index": 0}, {"node": "Microsoft Excel4", "type": "main", "index": 0}]]}, "Microsoft Excel4": {"main": [[{"node": "Sleep 0.8 second2", "type": "main", "index": 0}]]}, "Microsoft Excel5": {"main": [[{"node": "Sleep 0.8 second3", "type": "main", "index": 0}]]}, "Microsoft Excel6": {"main": [[{"node": "Set", "type": "main", "index": 0}]]}, "Set": {"main": [[{"node": "Microsoft Excel7", "type": "main", "index": 0}]]}, "Sleep 0.8 second": {"main": [[{"node": "Microsoft Excel3", "type": "main", "index": 0}]]}, "Sleep 0.8 second1": {"main": [[{"node": "Microsoft Excel1", "type": "main", "index": 0}]]}, "Sleep 0.8 second2": {"main": [[{"node": "Microsoft Excel5", "type": "main", "index": 0}]]}, "Sleep 0.8 second3": {"main": [[{"node": "Microsoft Excel6", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}