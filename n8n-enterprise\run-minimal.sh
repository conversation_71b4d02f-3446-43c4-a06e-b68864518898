#!/bin/bash
set -e

echo "🚀 Starting N8N Enterprise Edition..."
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

# 导入环境变量
if [ -f "/home/<USER>/.env" ]; then
    source /home/<USER>/.env
    echo "✅ Environment variables loaded"
else
    echo "⚠️  No .env file found, using default settings"
fi

# 显示企业版功能状态
echo ""
echo "🎯 N8N ENTERPRISE MOCK ENABLED"
echo "All enterprise features are unlocked for testing!"
echo ""
echo "Enterprise Features Available:"
echo "✅ SAML单点登录"
echo "✅ LDAP/Active Directory集成"
echo "✅ 高级权限管理"
echo "✅ 工作流和凭证共享"
echo "✅ 项目管理"
echo "✅ Git源代码控制"
echo "✅ 环境变量管理"
echo "✅ 外部密钥管理"
echo "✅ 工作流版本历史"
echo "✅ AI助手功能"
echo "✅ 多主实例高可用"
echo "✅ 日志流式传输"
echo ""

# 输出配置信息
echo "📊 Database Configuration:"
echo "Host: ${DB_POSTGRESDB_HOST:-not_set}"
echo "Port: ${DB_POSTGRESDB_PORT:-5432}"
echo "User: ${DB_POSTGRESDB_USER:-not_set}"
echo "Database: ${DB_POSTGRESDB_DATABASE:-not_set}"
echo "Type: ${DB_TYPE:-postgresdb}"
echo ""

# 设置 N8N 环境变量（如果配置文件存在）
if [ -f "/home/<USER>/n8n/config/n8n_env.sh" ]; then
    source /home/<USER>/n8n/config/n8n_env.sh
    echo "✅ N8N environment configuration loaded"
fi

# 确保必要的环境变量已设置
export N8N_ENTERPRISE_MOCK=true
export N8N_DEFAULT_LOCALE=zh-CN
export NODE_ENV=development
export N8N_RELEASE_TYPE=stable

echo ""
echo "🚀 Starting n8n Enterprise Edition..."
echo "Access your n8n instance at: ${WEBHOOK_URL:-https://your-space.hf.space/}"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo ""

# 启动 n8n
exec n8n start
