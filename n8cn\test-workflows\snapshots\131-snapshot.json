{"data": {"startData": {}, "resultData": {"runData": {"Start": [{"hints": [], "startTime": 1738078143830, "executionTime": 0, "source": [], "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "Line": [{"hints": [], "startTime": 1738078143831, "executionTime": 1446, "source": [{"previousNode": "Start"}], "executionStatus": "success", "data": {"main": [[{"json": {"status": 200, "message": "ok"}, "pairedItem": {"item": 0}}]]}}]}, "lastNodeExecuted": "Line"}, "executionData": {"contextData": {}, "nodeExecutionStack": [], "metadata": {}, "waitingExecution": {}, "waitingExecutionSource": {}}}, "mode": "cli", "startedAt": "2025-01-28T15:29:03.830Z", "stoppedAt": "2025-01-28T15:29:05.277Z", "status": "running", "finished": true}