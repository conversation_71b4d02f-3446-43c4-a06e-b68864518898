{"data": {"startData": {}, "resultData": {"runData": {"When clicking \"Test workflow\"": [{"startTime": 1709625796309, "executionTime": 0, "source": [], "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "Cohere Model": [{"startTime": 1709625796771, "executionTime": 354, "executionStatus": "success", "source": [null], "data": {"ai_languageModel": [[{"json": {"response": {"object": true}}}]]}, "inputOverride": {"ai_languageModel": [[{"json": {"messages": ["How much is 1+1? Only provide the numerical answer without any other text.\n"], "options": {"signal": {}}}}]]}}], "Cohere Instruct": [{"startTime": 1709625796309, "executionTime": 1133, "source": [{"previousNode": "When clicking \"Test workflow\""}], "executionStatus": "success", "data": {"main": [[{"json": {"text": "2"}, "pairedItem": {"item": 0}}]]}, "metadata": {"subRun": [{"node": "Cohere Model", "runIndex": 0}]}}]}, "lastNodeExecuted": "Cohere Instruct"}, "executionData": {"contextData": {}, "nodeExecutionStack": [], "metadata": {"Cohere Instruct": [{"subRun": [{"node": "Cohere Model", "runIndex": 0}]}]}, "waitingExecution": {}, "waitingExecutionSource": {}}}, "mode": "cli", "startedAt": "2024-03-05T08:03:16.304Z", "stoppedAt": "2024-03-05T08:03:17.443Z", "status": "running", "finished": true}