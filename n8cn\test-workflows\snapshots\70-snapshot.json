{"data": {"startData": {}, "resultData": {"runData": {"Start": [{"startTime": 1747344010966, "executionIndex": 0, "source": [], "hints": [], "executionTime": 0, "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "Contentful5": [{"startTime": 1747344010966, "executionIndex": 1, "source": [{"previousNode": "Start"}], "hints": [], "executionTime": 60, "executionStatus": "success", "data": {"main": [[{"json": {"code": "en-US", "name": "English (United States)", "default": true, "fallbackCode": {"object": true}, "sys": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "Contentful": [{"startTime": 1747344011026, "executionIndex": 2, "source": [{"previousNode": "Start"}], "hints": [], "executionTime": 61, "executionStatus": "success", "data": {"main": [[{"json": {"title": "Static sites are great", "slug": "static-sites-are-great", "heroImage": {"object": true}, "description": "Worry less about security, caching, and talking to the server. Static sites are the new thing.", "body": "## The case for the static site generator\n\nMore and more developers are jumping on the \"go static train\", and rightfully so. Static pages are fast, lightweight, they scale well. They are more secure, and simple to maintain and they allow you to focus all your time and effort on the user interface. Often times, this dedication really shows.\n\nIt just so happens that static site generators are mostly loved by developers, but not by the average <PERSON>. They do not offer WYSIWYG, previewing on demo sites may take an update cycle, they are often based on markdown text files, and they require some knowledge of modern day repositories.\n\nMoreover, when teams are collaborating, it can get complicated quickly. Has this article already been proof-read or reviewed? Is this input valid? Are user permissions available, e.g. for administering adding and removing team members? Can this article be published at a future date? How can a large repository of content be categorized, organized, and searched? All these requirements have previously been more or less solved within the admin area of your CMS. But of course with all the baggage that made you leave the appserver-app-database-in-one-big-blob stack in the first place.\n\n## Content APIs to the rescue\n\nAn alternative is decoupling the content management aspect from the system. And then replacing the maintenance prone server with a cloud based web service offering. Effectively, instead of your CMS of old, you move to a [Content Management as a Service (CMaaS)](https://www.contentful.com/r/knowledgebase/content-as-a-service/ \"Content Management as a Service (CMaaS)\") world, with a content API to deliver all your content. That way, you get the all the [benefits of content management features](http://www.digett.com/blog/01/16/2014/pairing-static-websites-cms \"benefits of content management features\") while still being able to embrace the static site generator mantra.\n\nIt so happens that Contentful is offering just that kind of content API. A service that\n\n* from the ground up has been designed to be fast, scalable, secure, and offer high uptime, so that you don’t have to worry about maintenance ever again.\n* offers a powerful editor and lots of flexibility in creating templates for your documents that your editors can reuse and combine, so that no developers resources are required in everyday writing and updating tasks.\n* separates content from presentation, so you can reuse your content repository for any device platform your heart desires. That way, you can COPE (\"create once, publish everywhere\").\n* offers webhooks that you can use to rebuild your static site in a fully automated fashion every time your content is modified.\n\nExtracted from the article [CMS-functionality for static site generators](https://www.contentful.com/r/knowledgebase/contentful-api-cms-static-site-generators/ \"CMS-functionality for static site generators\"). Read more about the [static site generators supported by Contentful](https://www.contentful.com/developers/docs/tools/staticsitegenerators/ \"static site generators supported by Contentful\").", "author": {"object": true}, "publishDate": "2017-05-16T00:00+02:00", "tags": ["json array"]}, "pairedItem": {"item": 0}}]]}}], "Contentful1": [{"startTime": 1747344011087, "executionIndex": 3, "source": [{"previousNode": "Start"}], "hints": [], "executionTime": 54, "executionStatus": "success", "data": {"main": [[{"json": {"id": "title", "name": "Title", "type": "Symbol", "localized": false, "required": true, "disabled": false, "omitted": false}, "pairedItem": {"item": 0}}, {"json": {"id": "slug", "name": "Slug", "type": "Symbol", "localized": false, "required": true, "disabled": false, "omitted": false}, "pairedItem": {"item": 0}}, {"json": {"id": "heroImage", "name": "Hero Image", "type": "Link", "localized": false, "required": true, "disabled": false, "omitted": false, "linkType": "<PERSON><PERSON>"}, "pairedItem": {"item": 0}}, {"json": {"id": "description", "name": "Description", "type": "Text", "localized": false, "required": true, "disabled": false, "omitted": false}, "pairedItem": {"item": 0}}, {"json": {"id": "body", "name": "Body", "type": "Text", "localized": false, "required": true, "disabled": false, "omitted": false}, "pairedItem": {"item": 0}}, {"json": {"id": "author", "name": "Author", "type": "Link", "localized": false, "required": false, "disabled": false, "omitted": false, "linkType": "Entry"}, "pairedItem": {"item": 0}}, {"json": {"id": "publishDate", "name": "Publish Date", "type": "Date", "localized": false, "required": true, "disabled": false, "omitted": false}, "pairedItem": {"item": 0}}, {"json": {"id": "tags", "name": "Tags", "type": "Array", "localized": false, "required": false, "disabled": false, "omitted": false, "items": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "Contentful2": [{"startTime": 1747344011141, "executionIndex": 4, "source": [{"previousNode": "Start"}], "hints": [], "executionTime": 51, "executionStatus": "success", "data": {"main": [[{"json": {"title": "TestAsset", "file": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "Contentful6": [{"startTime": 1747344011192, "executionIndex": 5, "source": [{"previousNode": "Start"}], "hints": [], "executionTime": 51, "executionStatus": "success", "data": {"main": [[{"json": {"sys": {"object": true}, "name": "n8n", "locales": ["json array"]}, "pairedItem": {"item": 0}}]]}}], "Contentful4": [{"startTime": 1747344011243, "executionIndex": 6, "source": [{"previousNode": "Contentful"}], "hints": [], "executionTime": 0, "executionStatus": "success", "data": {"main": [[{"json": {"title": "Static sites are great", "slug": "static-sites-are-great", "heroImage": {"object": true}, "description": "Worry less about security, caching, and talking to the server. Static sites are the new thing.", "body": "## The case for the static site generator\n\nMore and more developers are jumping on the \"go static train\", and rightfully so. Static pages are fast, lightweight, they scale well. They are more secure, and simple to maintain and they allow you to focus all your time and effort on the user interface. Often times, this dedication really shows.\n\nIt just so happens that static site generators are mostly loved by developers, but not by the average <PERSON>. They do not offer WYSIWYG, previewing on demo sites may take an update cycle, they are often based on markdown text files, and they require some knowledge of modern day repositories.\n\nMoreover, when teams are collaborating, it can get complicated quickly. Has this article already been proof-read or reviewed? Is this input valid? Are user permissions available, e.g. for administering adding and removing team members? Can this article be published at a future date? How can a large repository of content be categorized, organized, and searched? All these requirements have previously been more or less solved within the admin area of your CMS. But of course with all the baggage that made you leave the appserver-app-database-in-one-big-blob stack in the first place.\n\n## Content APIs to the rescue\n\nAn alternative is decoupling the content management aspect from the system. And then replacing the maintenance prone server with a cloud based web service offering. Effectively, instead of your CMS of old, you move to a [Content Management as a Service (CMaaS)](https://www.contentful.com/r/knowledgebase/content-as-a-service/ \"Content Management as a Service (CMaaS)\") world, with a content API to deliver all your content. That way, you get the all the [benefits of content management features](http://www.digett.com/blog/01/16/2014/pairing-static-websites-cms \"benefits of content management features\") while still being able to embrace the static site generator mantra.\n\nIt so happens that Contentful is offering just that kind of content API. A service that\n\n* from the ground up has been designed to be fast, scalable, secure, and offer high uptime, so that you don’t have to worry about maintenance ever again.\n* offers a powerful editor and lots of flexibility in creating templates for your documents that your editors can reuse and combine, so that no developers resources are required in everyday writing and updating tasks.\n* separates content from presentation, so you can reuse your content repository for any device platform your heart desires. That way, you can COPE (\"create once, publish everywhere\").\n* offers webhooks that you can use to rebuild your static site in a fully automated fashion every time your content is modified.\n\nExtracted from the article [CMS-functionality for static site generators](https://www.contentful.com/r/knowledgebase/contentful-api-cms-static-site-generators/ \"CMS-functionality for static site generators\"). Read more about the [static site generators supported by Contentful](https://www.contentful.com/developers/docs/tools/staticsitegenerators/ \"static site generators supported by Contentful\").", "author": {"object": true}, "publishDate": "2017-05-16T00:00+02:00", "tags": ["json array"]}, "pairedItem": {"item": 0}}]]}}], "Contentful3": [{"startTime": 1747344011243, "executionIndex": 7, "source": [{"previousNode": "Contentful2"}], "hints": [], "executionTime": 0, "executionStatus": "success", "data": {"main": [[{"json": {"title": "TestAsset", "file": {"object": true}}, "pairedItem": {"item": 0}}]]}}]}, "lastNodeExecuted": "Contentful3"}, "executionData": {"contextData": {}, "nodeExecutionStack": [], "metadata": {}, "waitingExecution": {}, "waitingExecutionSource": {}}}, "mode": "cli", "startedAt": "2025-05-15T21:20:10.966Z", "stoppedAt": "2025-05-15T21:20:11.243Z", "status": "running", "finished": true}