{"data": {"startData": {}, "resultData": {"runData": {"When clicking \"Test workflow\"": [{"startTime": 1747343998385, "executionIndex": 0, "source": [], "hints": [], "executionTime": 1, "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "AWS Bedrock Chat Model": [{"startTime": 1747343998389, "executionTime": 1163, "executionIndex": 2, "executionStatus": "success", "source": [{"previousNode": "AWS Bedrock Chat", "previousNodeRun": 0}], "data": {"ai_languageModel": [[{"json": {"response": {"object": true}, "tokenUsageEstimate": {"object": true}}}]]}, "inputOverride": {"ai_languageModel": [[{"json": {"messages": ["Human: How much is 1+1? Only provide the numerical answer without any other text.\n"], "estimatedTokens": 20, "options": {"lc": 1, "type": "not_implemented", "id": ["langchain", "chat_models", "chat_bedrock_converse", "ChatBedrockConverse"]}}}]]}, "metadata": {"subRun": [{"node": "AWS Bedrock Chat Model", "runIndex": 0}]}}], "AWS Bedrock Chat": [{"startTime": 1747343998386, "executionIndex": 1, "source": [{"previousNode": "When clicking \"Test workflow\""}], "hints": [], "executionTime": 1166, "executionStatus": "success", "data": {"main": [[{"json": {"text": "The answer to the equation 1+1 is 2."}, "pairedItem": {"item": 0}}]]}}]}, "lastNodeExecuted": "AWS Bedrock Chat"}, "executionData": {"contextData": {}, "nodeExecutionStack": [], "metadata": {"AWS Bedrock Chat Model": [{"subRun": [{"node": "AWS Bedrock Chat Model", "runIndex": 0}]}]}, "waitingExecution": {}, "waitingExecutionSource": {}}}, "mode": "cli", "startedAt": "2025-05-15T21:19:58.385Z", "stoppedAt": "2025-05-15T21:19:59.552Z", "status": "running", "finished": true}