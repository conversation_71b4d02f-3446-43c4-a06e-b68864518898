{"createdAt": "2021-03-01T11:14:09.665Z", "updatedAt": "2021-03-01T11:21:28.506Z", "id": "84", "name": "Matrix:Room:create invite kick leave:RoomMember:getAll:Message:create getAll:Account:me:Media:upload:Event:get", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300], "id": "4653989b-2d73-400a-b9e3-05248cca34be"}, {"parameters": {"resource": "account"}, "name": "Matrix", "type": "n8n-nodes-base.matrix", "typeVersion": 1, "position": [450, 100], "credentials": {"matrixApi": {"id": "71", "name": "Matrix creds"}}, "id": "47305d8a-ba2e-4bc3-8021-1abf32a300ae"}, {"parameters": {"resource": "room", "roomName": "=Room{{Date.now()}}"}, "name": "Matrix1", "type": "n8n-nodes-base.matrix", "typeVersion": 1, "position": [450, 300], "credentials": {"matrixApi": {"id": "71", "name": "Matrix creds"}}, "id": "5d6f2b69-6762-4376-b1bb-6b3d8b823c37"}, {"parameters": {"resource": "room", "operation": "invite", "roomId": "={{$node[\"Matrix1\"].json[\"room_id\"]}}", "userId": "@node8qa:matrix.org"}, "name": "Matrix2", "type": "n8n-nodes-base.matrix", "typeVersion": 1, "position": [1000, 200], "alwaysOutputData": true, "credentials": {"matrixApi": {"id": "71", "name": "Matrix creds"}}, "id": "939b7824-2a25-4b5a-9d11-57ee9107578c"}, {"parameters": {"resource": "room", "operation": "kick", "roomId": "={{$node[\"Matrix1\"].json[\"room_id\"]}}", "userId": "@node8qa:matrix.org", "reason": "test"}, "name": "Matrix3", "type": "n8n-nodes-base.matrix", "typeVersion": 1, "position": [1200, 200], "alwaysOutputData": true, "credentials": {"matrixApi": {"id": "71", "name": "Matrix creds"}}, "id": "e5d85928-b775-4eb5-9976-18075db86af0"}, {"parameters": {"resource": "room", "operation": "leave", "roomId": "={{$node[\"Matrix1\"].json[\"room_id\"]}}"}, "name": "Matrix4", "type": "n8n-nodes-base.matrix", "typeVersion": 1, "position": [1440, 420], "alwaysOutputData": true, "credentials": {"matrixApi": {"id": "71", "name": "Matrix creds"}}, "id": "7e684d64-ab4c-4de9-bd5b-ab1e8571c0d9"}, {"parameters": {"resource": "roomMember", "roomId": "={{$node[\"Matrix1\"].json[\"room_id\"]}}", "filters": {}}, "name": "Matrix5", "type": "n8n-nodes-base.matrix", "typeVersion": 1, "position": [700, 420], "alwaysOutputData": true, "credentials": {"matrixApi": {"id": "71", "name": "Matrix creds"}}, "id": "a3cd7697-4db7-4cc2-a8bf-68625273a76a"}, {"parameters": {"roomId": "={{$node[\"Matrix1\"].json[\"room_id\"]}}", "text": "=Test {{Date.now()}}"}, "name": "Matrix6", "type": "n8n-nodes-base.matrix", "typeVersion": 1, "position": [600, 200], "alwaysOutputData": true, "credentials": {"matrixApi": {"id": "71", "name": "Matrix creds"}}, "id": "9cbac5a1-e829-40d3-acd7-ad4fdb03c1ac"}, {"parameters": {"operation": "getAll", "roomId": "={{$node[\"Matrix1\"].json[\"room_id\"]}}", "limit": 1, "otherOptions": {}}, "name": "Matrix7", "type": "n8n-nodes-base.matrix", "typeVersion": 1, "position": [800, 200], "alwaysOutputData": true, "credentials": {"matrixApi": {"id": "71", "name": "Matrix creds"}}, "id": "205d5ab7-64ae-4722-b4bd-bb2a687de4b4"}, {"parameters": {"resource": "media", "roomId": "={{$node[\"Matrix1\"].json[\"room_id\"]}}"}, "name": "Matrix8", "type": "n8n-nodes-base.matrix", "typeVersion": 1, "position": [820, 620], "alwaysOutputData": true, "credentials": {"matrixApi": {"id": "71", "name": "Matrix creds"}}, "id": "6aa74367-4d6a-495f-885e-14568c493ca1"}, {"parameters": {"filePath": "/tmp/n8n-logo.png"}, "name": "Read Binary File", "type": "n8n-nodes-base.readBinaryFile", "typeVersion": 1, "position": [600, 620], "id": "07201442-b394-41cb-90e7-1a3f3f5e414f"}, {"parameters": {"resource": "event", "roomId": "={{$node[\"Matrix1\"].json[\"room_id\"]}}", "eventId": "={{$node[\"Matrix8\"].json[\"event_id\"]}}"}, "name": "Matrix9", "type": "n8n-nodes-base.matrix", "typeVersion": 1, "position": [1020, 620], "alwaysOutputData": true, "credentials": {"matrixApi": {"id": "71", "name": "Matrix creds"}}, "id": "a0c2d949-47f2-49a4-90fd-7ca465c2c579"}, {"parameters": {"mode": "chooseBranch"}, "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "typeVersion": 2.1, "position": [1240, 420], "id": "a3b8276c-5961-4508-a8c1-5d9144436ff5"}], "connections": {"Matrix1": {"main": [[{"node": "Matrix6", "type": "main", "index": 0}, {"node": "Matrix5", "type": "main", "index": 0}, {"node": "Read Binary File", "type": "main", "index": 0}]]}, "Matrix2": {"main": [[{"node": "Matrix3", "type": "main", "index": 0}]]}, "Matrix3": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Matrix6": {"main": [[{"node": "Matrix7", "type": "main", "index": 0}]]}, "Matrix7": {"main": [[{"node": "Matrix2", "type": "main", "index": 0}]]}, "Read Binary File": {"main": [[{"node": "Matrix8", "type": "main", "index": 0}]]}, "Matrix8": {"main": [[{"node": "Matrix9", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "Matrix1", "type": "main", "index": 0}, {"node": "Matrix", "type": "main", "index": 0}]]}, "Matrix5": {"main": [[]]}, "Matrix9": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Matrix4": {"main": [[]]}, "Merge": {"main": [[{"node": "Matrix4", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}