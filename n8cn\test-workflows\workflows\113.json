{"createdAt": "2021-03-09T14:26:06.900Z", "updatedAt": "2021-03-09T17:25:17.607Z", "id": "113", "name": "AMQP", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300], "id": "cb0c801c-e74c-43a6-9b4d-6e3c2b265f22"}, {"parameters": {"sink": "AMQPQueue", "headerParametersJson": "{}", "options": {"reconnect": true, "reconnectLimit": 5}}, "name": "AMQP Sender", "type": "n8n-nodes-base.amqp", "typeVersion": 1, "position": [650, 300], "credentials": {"amqp": {"id": "80", "name": "AMQP creds"}}, "id": "7d7a98e3-27b0-49bf-8c68-a525895d0b8c"}, {"parameters": {"keepOnlySet": true, "values": {"string": [{"name": "message", "value": "=AMQPMessage{{Date.now()}}"}]}, "options": {}}, "name": "Set", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [450, 300], "id": "951c5927-93e8-4efb-924a-e6516679017c"}], "connections": {"Start": {"main": [[{"node": "Set", "type": "main", "index": 0}]]}, "AMQP Sender": {"main": [[]]}, "Set": {"main": [[{"node": "AMQP Sender", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}