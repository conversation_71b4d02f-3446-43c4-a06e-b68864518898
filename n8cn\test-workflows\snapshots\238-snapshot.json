{"data": {"startData": {}, "resultData": {"runData": {"When clicking \"Test workflow\"": [{"startTime": 1747343996969, "executionIndex": 0, "source": [], "hints": [], "executionTime": 0, "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "OpenAI Model": [{"startTime": 1747343996970, "executionTime": 422, "executionIndex": 2, "executionStatus": "success", "source": [{"previousNode": "Open AI Instruct", "previousNodeRun": 0}], "data": {"ai_languageModel": [[{"json": {"response": {"object": true}, "tokenUsage": {"object": true}}}]]}, "inputOverride": {"ai_languageModel": [[{"json": {"messages": ["How much is 1+1? Only provide the numerical answer without any other text.\n\n"], "estimatedTokens": 18, "options": {"openai_api_key": {"lc": 1, "type": "secret", "id": ["OPENAI_API_KEY"]}, "model": "gpt-3.5-turbo-instruct", "temperature": 0, "configuration": {}, "timeout": 60000, "max_retries": 2}}}]]}, "metadata": {"subRun": [{"node": "OpenAI Model", "runIndex": 0}]}}], "Open AI Instruct": [{"startTime": 1747343996969, "executionIndex": 1, "source": [{"previousNode": "When clicking \"Test workflow\""}], "hints": [], "executionTime": 423, "executionStatus": "success", "data": {"main": [[{"json": {"text": "2"}, "pairedItem": {"item": 0}}]]}}]}, "lastNodeExecuted": "Open AI Instruct"}, "executionData": {"contextData": {}, "nodeExecutionStack": [], "metadata": {"OpenAI Model": [{"subRun": [{"node": "OpenAI Model", "runIndex": 0}]}]}, "waitingExecution": {}, "waitingExecutionSource": {}}}, "mode": "cli", "startedAt": "2025-05-15T21:19:56.969Z", "stoppedAt": "2025-05-15T21:19:57.392Z", "status": "running", "finished": true}