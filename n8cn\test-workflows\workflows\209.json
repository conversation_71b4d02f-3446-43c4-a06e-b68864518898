{"createdAt": "2021-05-31T08:24:33.353Z", "updatedAt": "2021-05-31T08:38:47.411Z", "id": "209", "name": "AWS Transcribe:TranscriptionJob:create get getAll delete", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300], "id": "cb687f80-1046-40f3-bddb-78a67623b70e"}, {"parameters": {"transcriptionJobName": "={{$node[\"Set job name\"].json[\"job_name\"]}}", "mediaFileUri": "s3://fixedtestbucket/nodemation.wav", "options": {}}, "name": "AWS Transcribe", "type": "n8n-nodes-base.awsTranscribe", "typeVersion": 1, "position": [550, 300], "credentials": {"aws": {"id": "124", "name": "AWS creds"}}, "id": "ccdefb6d-7aac-4360-9285-135ddf34449b"}, {"parameters": {"operation": "get", "transcriptionJobName": "={{$node[\"Set job name\"].json[\"job_name\"]}}", "simple": false}, "name": "AWS Transcribe1", "type": "n8n-nodes-base.awsTranscribe", "typeVersion": 1, "position": [700, 300], "credentials": {"aws": {"id": "124", "name": "AWS creds"}}, "id": "3840763a-16d3-4073-a008-9b5dae451d48"}, {"parameters": {"operation": "getAll", "limit": 1, "filters": {}}, "name": "AWS Transcribe2", "type": "n8n-nodes-base.awsTranscribe", "typeVersion": 1, "position": [850, 300], "credentials": {"aws": {"id": "124", "name": "AWS creds"}}, "id": "b477493b-0688-47f4-ad5c-4a1f509e672e"}, {"parameters": {"operation": "delete", "transcriptionJobName": "={{$node[\"Set job name\"].json[\"job_name\"]}}"}, "name": "AWS Transcribe3", "type": "n8n-nodes-base.awsTranscribe", "typeVersion": 1, "position": [1150, 300], "credentials": {"aws": {"id": "124", "name": "AWS creds"}}, "id": "f7c49172-c16a-4881-951b-c35927fe9ea4"}, {"parameters": {"values": {"string": [{"name": "job_name", "value": "=nodemation_job_{{Date.now()}}"}]}, "options": {}}, "name": "Set job name", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [400, 300], "id": "e96a125a-10fb-4d1a-869e-3e31467b1bb6"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(30000);\n\n// Output data\nreturn items;"}, "name": "Sleep 30 seconds", "type": "n8n-nodes-base.function", "position": [1000, 300], "typeVersion": 1, "id": "93a0b1db-6bda-4e6d-9797-6a3957cf05d0"}], "connections": {"AWS Transcribe": {"main": [[{"node": "AWS Transcribe1", "type": "main", "index": 0}]]}, "AWS Transcribe1": {"main": [[{"node": "AWS Transcribe2", "type": "main", "index": 0}]]}, "AWS Transcribe2": {"main": [[{"node": "Sleep 30 seconds", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "Set job name", "type": "main", "index": 0}]]}, "Set job name": {"main": [[{"node": "AWS Transcribe", "type": "main", "index": 0}]]}, "Sleep 30 seconds": {"main": [[{"node": "AWS Transcribe3", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}