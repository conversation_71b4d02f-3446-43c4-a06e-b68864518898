{"type": "object", "properties": {"_links": {"type": "object", "properties": {"collection": {"type": "array", "items": {"type": "object", "properties": {"href": {"type": "string"}}}}, "self": {"type": "array", "items": {"type": "object", "properties": {"href": {"type": "string"}}}}}}, "description": {"type": "string"}, "id": {"type": "integer"}, "link": {"type": "string"}, "name": {"type": "string"}, "slug": {"type": "string"}, "url": {"type": "string"}}, "version": 1}