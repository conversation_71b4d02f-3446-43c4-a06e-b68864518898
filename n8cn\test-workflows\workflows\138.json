{"createdAt": "2021-03-16T16:58:12.352Z", "updatedAt": "2021-05-21T09:27:19.124Z", "id": "138", "name": "TheHive[v4]:Alert:create update get getAll promote merge:Case:create update get getAll:Observable:create update get search getAll:Task:create update get search getAll:Log:create get getAll", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300], "id": "9f90439e-037b-46fd-b837-9b5c85b79e77"}, {"parameters": {"title": "=Title{{Date.now()}}", "description": "desc", "date": "={{(new Date()).toISOString()}}", "tags": "test", "type": "test", "source": "n8n", "sourceRef": "={{Date.now().toString()}}", "additionalFields": {}}, "name": "TheHive", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [470, 50], "credentials": {"theHiveApi": {"id": "108", "name": "The Hive API creds (v1)"}}, "id": "aa971fff-c258-4a9b-b8ee-d02ff2f3742f"}, {"parameters": {"operation": "update", "id": "={{$node[\"TheHive\"].json[\"id\"]}}", "updateFields": {"tlp": 1}}, "name": "TheHive1", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [620, 50], "credentials": {"theHiveApi": {"id": "108", "name": "The Hive API creds (v1)"}}, "id": "f84362fd-61df-4f44-933e-6a8f0c1190ca"}, {"parameters": {"operation": "get", "id": "={{$node[\"TheHive\"].json[\"id\"]}}"}, "name": "TheHive2", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [970, 50], "credentials": {"theHiveApi": {"id": "108", "name": "The Hive API creds (v1)"}}, "id": "60e238b5-7826-4338-8367-9bf8a5ac1867"}, {"parameters": {"operation": "getAll", "limit": 1, "options": {}, "filters": {}}, "name": "TheHive3", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [1120, 50], "credentials": {"theHiveApi": {"id": "108", "name": "The Hive API creds (v1)"}}, "id": "4d8575f0-4d93-41bc-acb7-518e89948e3e"}, {"parameters": {"operation": "promote", "id": "={{$node[\"TheHive\"].json[\"id\"]}}", "additionalFields": {}}, "name": "TheHive4", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [1430, 50], "credentials": {"theHiveApi": {"id": "108", "name": "The Hive API creds (v1)"}}, "id": "04f0c48e-95e9-4507-b1f4-3a839118ba61"}, {"parameters": {"resource": "case", "operation": "create", "title": "=Title{{Date.now()}}", "description": "desc", "startDate": "={{(new Date()).toISOString()}}", "owner": "nodeqa", "tags": "test", "options": {}}, "name": "TheHive6", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [470, 350], "credentials": {"theHiveApi": {"id": "108", "name": "The Hive API creds (v1)"}}, "id": "0b101032-87b6-4b59-9d31-b2b6622518c6"}, {"parameters": {"resource": "case", "operation": "update", "id": "={{$node[\"TheHive6\"].json[\"caseId\"]}}", "updateFields": {"tlp": 3}}, "name": "TheHive7", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [620, 350], "credentials": {"theHiveApi": {"id": "108", "name": "The Hive API creds (v1)"}}, "id": "7da42dc0-c2a1-438e-9e44-329dee84938e"}, {"parameters": {"resource": "case", "operation": "get", "id": "={{$node[\"TheHive6\"].json[\"id\"]}}"}, "name": "TheHive8", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [950, 350], "credentials": {"theHiveApi": {"id": "108", "name": "The Hive API creds (v1)"}}, "id": "07902779-ae8c-4c8d-a300-57e86244cd7d"}, {"parameters": {"resource": "case", "limit": 1, "options": {}, "filters": {}}, "name": "TheHive9", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [1100, 350], "credentials": {"theHiveApi": {"id": "108", "name": "The Hive API creds (v1)"}}, "id": "e45a3b35-0452-47b5-9136-521a69f1daed"}, {"parameters": {"resource": "task", "operation": "create", "caseId": "={{$node[\"TheHive6\"].json[\"caseId\"]}}", "title": "=Task{{Date.now()}}", "options": {}}, "name": "TheHive11", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [620, 520], "credentials": {"theHiveApi": {"id": "108", "name": "The Hive API creds (v1)"}}, "id": "8bd67bbf-9b6c-43fe-b88e-4575f2a4206e"}, {"parameters": {"resource": "task", "operation": "update", "id": "={{$node[\"TheHive11\"].json[\"id\"]}}", "updateFields": {"status": "InProgress"}}, "name": "TheHive12", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [770, 520], "credentials": {"theHiveApi": {"id": "108", "name": "The Hive API creds (v1)"}}, "id": "1df94f03-a17b-47fe-8711-06e6c973c4cd"}, {"parameters": {"resource": "task", "operation": "get", "id": "={{$node[\"TheHive11\"].json[\"id\"]}}"}, "name": "TheHive13", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [1080, 520], "credentials": {"theHiveApi": {"id": "108", "name": "The Hive API creds (v1)"}}, "id": "d766828e-f1d4-47b1-82ef-21ad8b72ac08"}, {"parameters": {"resource": "task", "operation": "search", "limit": 1, "options": {}, "filters": {}}, "name": "TheHive14", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [1230, 520], "credentials": {"theHiveApi": {"id": "108", "name": "The Hive API creds (v1)"}}, "id": "ddeb0b79-5381-4151-808d-66ba52861c11"}, {"parameters": {"resource": "log", "operation": "create", "taskId": "={{$node[\"TheHive11\"].json[\"id\"]}}", "message": "=Message{{Date.now()}}", "startDate": "={{(new Date()).toISOString()}}", "status": "Ok", "options": {}}, "name": "TheHive16", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [770, 680], "credentials": {"theHiveApi": {"id": "108", "name": "The Hive API creds (v1)"}}, "id": "b98e4f38-08b0-4dda-949f-9a1d2f02a36e"}, {"parameters": {"resource": "log", "operation": "get", "id": "={{$node[\"TheHive16\"].json[\"_id\"]}}"}, "name": "TheHive17", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [1090, 680], "credentials": {"theHiveApi": {"id": "108", "name": "The Hive API creds (v1)"}}, "id": "f14cd395-0d27-4342-8f42-051bdec28e09"}, {"parameters": {"resource": "log", "taskId": "={{$node[\"TheHive16\"].json[\"_id\"]}}", "limit": 1}, "name": "TheHive18", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [1240, 680], "credentials": {"theHiveApi": {"id": "108", "name": "The Hive API creds (v1)"}}, "id": "6f0f685c-de2f-4a5a-b221-fea2b1f9fedf"}, {"parameters": {"resource": "observable", "operation": "create", "caseId": "={{$node[\"TheHive6\"].json[\"caseId\"]}}", "dataType": "ip", "data": "**************", "message": "test", "startDate": "={{(new Date()).toISOString()}}", "ioc": true, "status": "Ok", "options": {}}, "name": "TheHive19", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [620, 200], "credentials": {"theHiveApi": {"id": "108", "name": "The Hive API creds (v1)"}}, "id": "93e80467-34a8-45ba-aa62-77b9b85f5091"}, {"parameters": {"resource": "observable", "operation": "update", "id": "={{$node[\"TheHive19\"].json[\"_id\"]}}", "updateFields": {"ioc": false}}, "name": "TheHive20", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [940, 200], "credentials": {"theHiveApi": {"id": "108", "name": "The Hive API creds (v1)"}}, "id": "34ff3179-4e8b-4947-b5e2-d99a51ab3865"}, {"parameters": {"resource": "observable", "operation": "get", "id": "={{$node[\"TheHive19\"].json[\"_id\"]}}"}, "name": "TheHive21", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [1090, 200], "credentials": {"theHiveApi": {"id": "108", "name": "The Hive API creds (v1)"}}, "id": "6292663d-fd3f-48b0-8802-fd0d9e29beb0"}, {"parameters": {"resource": "observable", "operation": "search", "limit": 1, "options": {}, "filters": {}}, "name": "TheHive22", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [1450, 200], "credentials": {"theHiveApi": {"id": "108", "name": "The Hive API creds (v1)"}}, "id": "5e58213d-6a75-4304-9b10-0b1e9137d436"}, {"parameters": {"resource": "observable", "caseId": "={{$node[\"TheHive6\"].json[\"_id\"]}}", "limit": 1, "options": {}}, "name": "TheHive23", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [1600, 200], "credentials": {"theHiveApi": {"id": "108", "name": "The Hive API creds (v1)"}}, "id": "35a6f784-f1d4-482d-b7b1-7d1e7fe12383"}, {"parameters": {"resource": "task", "caseId": "={{$node[\"TheHive6\"].json[\"_id\"]}}", "limit": 1, "options": {}}, "name": "TheHive26", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [1520, 520], "credentials": {"theHiveApi": {"id": "108", "name": "The Hive API creds (v1)"}}, "id": "3af01f2d-8d05-46f6-bc4e-d299e7b36117"}, {"parameters": {"resource": "log", "operation": "executeResponder"}, "name": "TheHive28", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [1390, 680], "credentials": {"theHiveApi": {"id": "108", "name": "The Hive API creds (v1)"}}, "disabled": true, "id": "a6c442cd-4720-45bb-bbc4-360e7aa523f7"}, {"parameters": {"operation": "count", "filters": {}}, "name": "TheHive29", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [1600, 50], "credentials": {"theHiveApi": {"id": "108", "name": "The Hive API creds (v1)"}}, "id": "129264d0-06f9-4705-8b6a-1656f3e444ac"}, {"parameters": {"resource": "observable", "operation": "count", "filters": {}}, "name": "TheHive30", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [1730, 200], "credentials": {"theHiveApi": {"id": "108", "name": "The Hive API creds (v1)"}}, "id": "98c5ecaf-005a-4c35-ad42-42eb9b507fdf"}, {"parameters": {"resource": "case", "operation": "count", "filters": {}}, "name": "TheHive31", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [1460, 350], "credentials": {"theHiveApi": {"id": "108", "name": "The Hive API creds (v1)"}}, "id": "fc75071b-b04c-4b46-8001-84e4be11b410"}, {"parameters": {"resource": "task", "operation": "count", "filters": {}}, "name": "TheHive32", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [1650, 520], "credentials": {"theHiveApi": {"id": "108", "name": "The Hive API creds (v1)"}}, "id": "ab81354d-86ed-454d-8a8c-179f3bfffcac"}, {"parameters": {"operation": "merge", "id": "={{$node[\"TheHive35\"].json[\"id\"]}}", "caseId": "={{$node[\"TheHive34\"].json[\"id\"]}}"}, "name": "TheHive33", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [900, -100], "credentials": {"theHiveApi": {"id": "108", "name": "The Hive API creds (v1)"}}, "id": "d0348684-a348-47ad-b9bd-bb5ef60eb620"}, {"parameters": {"resource": "case", "operation": "create", "title": "=MergingCase{{Date.now()}}", "description": "desc", "startDate": "={{(new Date()).toISOString()}}", "owner": "nodeqa", "tags": "test", "options": {}}, "name": "TheHive34", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [470, -100], "credentials": {"theHiveApi": {"id": "108", "name": "The Hive API creds (v1)"}}, "id": "363b6fcf-f673-485f-b50b-11a2f8274599"}, {"parameters": {"title": "=MergingAlert{{Date.now()}}", "description": "desc", "date": "={{(new Date()).toISOString()}}", "tags": "test", "type": "test", "source": "n8n", "sourceRef": "={{Date.now().toString()}}", "additionalFields": {}}, "name": "TheHive35", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [750, -100], "credentials": {"theHiveApi": {"id": "108", "name": "The Hive API creds (v1)"}}, "id": "b972bf61-8365-42f8-b1b4-bfddb2ea763c"}, {"parameters": {"operation": "executeResponder", "id": "={{$node[\"TheHive\"].json[\"id\"]}}", "responder": "23bc4aef9aa1c88d6624004a3d04aeae"}, "name": "TheHive15", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [1770, 50], "credentials": {"theHiveApi": {"id": "107", "name": "The Hive API creds"}}, "id": "edd53027-11e7-45df-ac58-38e84ea8e68f"}, {"parameters": {"resource": "observable", "operation": "executeResponder", "id": "={{$node[\"TheHive21\"].json[\"_id\"]}}", "responder": "fbe415a38eb649eb7df174aa11a32cfe"}, "name": "TheHive25", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [2060, 200], "credentials": {"theHiveApi": {"id": "107", "name": "The Hive API creds"}}, "id": "9cc1c6e0-7467-4143-af7a-18d0927ef266"}, {"parameters": {"resource": "observable", "operation": "executeAnalyzer", "id": "={{$node[\"TheHive21\"].json[\"_id\"]}}", "dataType": "={{$node[\"TheHive21\"].json[\"dataType\"]}}", "analyzers": ["6fdd3c9b5432f1e2094cd3b8f2347d09::cortex"]}, "name": "TheHive24", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [1880, 200], "credentials": {"theHiveApi": {"id": "107", "name": "The Hive API creds"}}, "id": "dd820c65-bf81-4ccb-9f5e-4687c69c60f9"}, {"parameters": {"resource": "case", "operation": "executeResponder", "id": "={{$node[\"TheHive6\"].json[\"id\"]}}", "responder": "23bc4aef9aa1c88d6624004a3d04aeae"}, "name": "TheHive10", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [1620, 350], "credentials": {"theHiveApi": {"id": "107", "name": "The Hive API creds"}}, "id": "c28cb76f-22b4-4651-a6be-97fbed03f9a2"}, {"parameters": {"resource": "task", "operation": "executeResponder", "id": "={{$node[\"TheHive11\"].json[\"id\"]}}", "responder": "23bc4aef9aa1c88d6624004a3d04aeae"}, "name": "TheHive27", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [1810, 520], "credentials": {"theHiveApi": {"id": "107", "name": "The Hive API creds"}}, "id": "faf84c8e-59fa-4cd7-9b96-b6d41ff7fd3e"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(500);\n\n// Output data\nreturn items;"}, "name": "Sleep 0.5 second", "type": "n8n-nodes-base.function", "position": [600, -100], "typeVersion": 1, "id": "48448770-8579-45c5-8b44-a3630182989b"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(500);\n\n// Output data\nreturn items;"}, "name": "Sleep 0.5 second1", "type": "n8n-nodes-base.function", "position": [770, 50], "typeVersion": 1, "id": "56f919d6-ba81-41f5-80f5-f5b1f57edc6a"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(500);\n\n// Output data\nreturn items;"}, "name": "Sleep 0.5 second2", "type": "n8n-nodes-base.function", "position": [780, 200], "typeVersion": 1, "id": "cc581b90-c49e-45dc-a74f-2058b14f54e6"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(500);\n\n// Output data\nreturn items;"}, "name": "Sleep 0.5 second3", "type": "n8n-nodes-base.function", "position": [790, 350], "typeVersion": 1, "id": "b11a7b70-aa7a-4c94-988e-04576c28ec06"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(500);\n\n// Output data\nreturn items;"}, "name": "Sleep 0.5 second4", "type": "n8n-nodes-base.function", "position": [920, 520], "typeVersion": 1, "id": "00e38394-b83d-4b2d-8f64-f002f3dee288"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(500);\n\n// Output data\nreturn items;"}, "name": "Sleep 0.5 second5", "type": "n8n-nodes-base.function", "position": [930, 680], "typeVersion": 1, "id": "203e4d68-f936-4f70-88cc-162cb3754faf"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(500);\n\n// Output data\nreturn items;"}, "name": "Sleep 0.5 second6", "type": "n8n-nodes-base.function", "position": [1380, 520], "typeVersion": 1, "id": "dc7c936c-b400-402e-af72-927ca98ac0ea"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(500);\n\n// Output data\nreturn items;"}, "name": "Sleep 0.5 second7", "type": "n8n-nodes-base.function", "position": [1280, 350], "typeVersion": 1, "id": "733a450f-5c8b-4451-b542-afbbd1757233"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(500);\n\n// Output data\nreturn items;"}, "name": "Sleep 0.5 second8", "type": "n8n-nodes-base.function", "position": [1260, 200], "typeVersion": 1, "id": "96ae2c59-bde3-4c79-be16-525e36e0634f"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(500);\n\n// Output data\nreturn items;"}, "name": "Sleep 0.5 second9", "type": "n8n-nodes-base.function", "position": [1270, 50], "typeVersion": 1, "id": "53151c11-2f85-4596-920a-4770739fd32e"}], "connections": {"TheHive": {"main": [[{"node": "TheHive1", "type": "main", "index": 0}]]}, "TheHive1": {"main": [[{"node": "Sleep 0.5 second1", "type": "main", "index": 0}]]}, "TheHive2": {"main": [[{"node": "TheHive3", "type": "main", "index": 0}]]}, "TheHive3": {"main": [[{"node": "TheHive15", "type": "main", "index": 0}, {"node": "Sleep 0.5 second9", "type": "main", "index": 0}]]}, "TheHive6": {"main": [[{"node": "TheHive7", "type": "main", "index": 0}, {"node": "TheHive11", "type": "main", "index": 0}, {"node": "TheHive19", "type": "main", "index": 0}]]}, "TheHive7": {"main": [[{"node": "Sleep 0.5 second3", "type": "main", "index": 0}]]}, "TheHive8": {"main": [[{"node": "TheHive9", "type": "main", "index": 0}, {"node": "TheHive10", "type": "main", "index": 0}]]}, "TheHive11": {"main": [[{"node": "TheHive12", "type": "main", "index": 0}, {"node": "TheHive16", "type": "main", "index": 0}]]}, "TheHive12": {"main": [[{"node": "Sleep 0.5 second4", "type": "main", "index": 0}]]}, "TheHive13": {"main": [[{"node": "TheHive14", "type": "main", "index": 0}, {"node": "TheHive27", "type": "main", "index": 0}]]}, "TheHive14": {"main": [[{"node": "Sleep 0.5 second6", "type": "main", "index": 0}]]}, "TheHive16": {"main": [[{"node": "Sleep 0.5 second5", "type": "main", "index": 0}]]}, "TheHive17": {"main": [[{"node": "TheHive18", "type": "main", "index": 0}]]}, "TheHive19": {"main": [[{"node": "Sleep 0.5 second2", "type": "main", "index": 0}]]}, "TheHive20": {"main": [[{"node": "TheHive21", "type": "main", "index": 0}]]}, "TheHive21": {"main": [[{"node": "TheHive24", "type": "main", "index": 0}, {"node": "TheHive25", "type": "main", "index": 0}, {"node": "Sleep 0.5 second8", "type": "main", "index": 0}]]}, "TheHive22": {"main": [[{"node": "TheHive23", "type": "main", "index": 0}]]}, "TheHive26": {"main": [[{"node": "TheHive32", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "TheHive6", "type": "main", "index": 0}, {"node": "TheHive", "type": "main", "index": 0}, {"node": "TheHive34", "type": "main", "index": 0}]]}, "TheHive4": {"main": [[{"node": "TheHive29", "type": "main", "index": 0}]]}, "TheHive23": {"main": [[{"node": "TheHive30", "type": "main", "index": 0}]]}, "TheHive9": {"main": [[{"node": "Sleep 0.5 second7", "type": "main", "index": 0}]]}, "TheHive34": {"main": [[{"node": "Sleep 0.5 second", "type": "main", "index": 0}]]}, "TheHive35": {"main": [[{"node": "TheHive33", "type": "main", "index": 0}]]}, "Sleep 0.5 second5": {"main": [[{"node": "TheHive17", "type": "main", "index": 0}]]}, "Sleep 0.5 second4": {"main": [[{"node": "TheHive13", "type": "main", "index": 0}]]}, "Sleep 0.5 second3": {"main": [[{"node": "TheHive8", "type": "main", "index": 0}]]}, "Sleep 0.5 second2": {"main": [[{"node": "TheHive20", "type": "main", "index": 0}]]}, "Sleep 0.5 second1": {"main": [[{"node": "TheHive2", "type": "main", "index": 0}]]}, "Sleep 0.5 second": {"main": [[{"node": "TheHive35", "type": "main", "index": 0}]]}, "Sleep 0.5 second6": {"main": [[{"node": "TheHive26", "type": "main", "index": 0}]]}, "Sleep 0.5 second7": {"main": [[{"node": "TheHive31", "type": "main", "index": 0}]]}, "Sleep 0.5 second8": {"main": [[{"node": "TheHive22", "type": "main", "index": 0}]]}, "Sleep 0.5 second9": {"main": [[{"node": "TheHive4", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}