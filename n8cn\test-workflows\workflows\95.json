{"createdAt": "2021-03-03T14:28:28.227Z", "updatedAt": "2021-03-03T14:28:29.476Z", "id": "95", "name": "Set", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300], "id": "32be2fcf-b143-442e-877f-96b51b0b5148"}, {"parameters": {"values": {"string": [{"name": "name", "value": "test"}]}, "options": {}}, "name": "Set", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [430, 300], "id": "17c6fa30-1a0e-4d29-8c73-ef9357d5f877"}, {"parameters": {"functionCode": "testData =  JSON.stringify({\n  name: \"test\"\n});\n\nif(JSON.stringify($node['Set'].json)!==testData){\n  throw new Error('Error in Set node');\n}\nreturn items;"}, "name": "Function", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [600, 300], "notesInFlow": true, "notes": "Verify the result of set node", "id": "c6e9a289-5551-4db5-8d32-a11953bc92d6"}], "connections": {"Set": {"main": [[{"node": "Function", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "Set", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}