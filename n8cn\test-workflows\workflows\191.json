{"createdAt": "2021-04-30T07:16:09.288Z", "updatedAt": "2021-04-30T07:16:09.288Z", "id": "191", "name": "Mailerlite:Subscriber:create get update getAll", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300], "id": "c998f9cf-a589-4a12-8f24-c5ad324ba4ad"}, {"parameters": {"email": "=fake{{Date.now()}}@test.com", "additionalFields": {}}, "name": "MailerLite", "type": "n8n-nodes-base.mailerLite", "typeVersion": 1, "position": [450, 300], "credentials": {"mailerLiteApi": {"id": "154", "name": "Mailer Lite API creds"}}, "id": "4de4bbd8-1214-4d0d-81fa-879332f6bca5"}, {"parameters": {"operation": "get", "subscriberId": "={{$node[\"MailerLite\"].json[\"email\"]}}"}, "name": "MailerLite1", "type": "n8n-nodes-base.mailerLite", "typeVersion": 1, "position": [600, 300], "credentials": {"mailerLiteApi": {"id": "154", "name": "Mailer Lite API creds"}}, "id": "d539fbfb-0228-4101-9e31-493abab14049"}, {"parameters": {"operation": "update", "subscriberId": "={{$node[\"MailerLite\"].json[\"email\"]}}", "updateFields": {"name": "=UpdatedName{{Date.now()}}"}}, "name": "MailerLite2", "type": "n8n-nodes-base.mailerLite", "typeVersion": 1, "position": [750, 300], "credentials": {"mailerLiteApi": {"id": "154", "name": "Mailer Lite API creds"}}, "id": "a56e6d61-cab2-4556-8d6d-e2f1f549c9d0"}, {"parameters": {"operation": "getAll", "limit": 1, "filters": {}}, "name": "MailerLite3", "type": "n8n-nodes-base.mailerLite", "typeVersion": 1, "position": [900, 300], "credentials": {"mailerLiteApi": {"id": "154", "name": "Mailer Lite API creds"}}, "id": "6083faa2-8837-49df-8eac-3515986418b2"}], "connections": {"MailerLite": {"main": [[{"node": "MailerLite1", "type": "main", "index": 0}]]}, "MailerLite1": {"main": [[{"node": "MailerLite2", "type": "main", "index": 0}]]}, "MailerLite2": {"main": [[{"node": "MailerLite3", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "MailerLite", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}