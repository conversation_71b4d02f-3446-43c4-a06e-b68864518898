{"createdAt": "2021-03-10T09:08:07.793Z", "updatedAt": "2021-03-10T09:08:07.793Z", "id": "115", "name": "Cockpit:Collection:createEntry updateEntry getAllEntries:Singleton:get:Form:submit", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300], "id": "768b2ddf-9888-4361-8f1e-3a4dff195e6b"}, {"parameters": {"operation": "create", "collection": "FixedCollection", "dataFieldsUi": {"field": [{"name": "Name", "value": "=entry{{Date.now()}}"}]}}, "name": "Cockpit", "type": "n8n-nodes-base.cockpit", "typeVersion": 1, "position": [550, 180], "credentials": {"cockpitApi": {"id": "81", "name": "Cockpit API creds"}}, "id": "a731c3d7-ffb7-493e-a3ac-9fdef8776801"}, {"parameters": {"operation": "update", "collection": "FixedCollection", "id": "={{$node[\"Cockpit\"].json[\"_id\"]}}", "dataFieldsUi": {"field": [{"name": "Name", "value": "=UpdatedEntry{{Date.now()}}"}]}}, "name": "Cockpit1", "type": "n8n-nodes-base.cockpit", "typeVersion": 1, "position": [700, 180], "credentials": {"cockpitApi": {"id": "81", "name": "Cockpit API creds"}}, "id": "be0de116-a348-436c-8903-a78f2d9978f3"}, {"parameters": {"collection": "FixedCollection", "limit": 1, "options": {}}, "name": "Cockpit2", "type": "n8n-nodes-base.cockpit", "typeVersion": 1, "position": [850, 180], "credentials": {"cockpitApi": {"id": "81", "name": "Cockpit API creds"}}, "id": "e151e242-d0a9-43d9-8003-632b4f8569ad"}, {"parameters": {"resource": "singleton", "singleton": "FixedSingleton"}, "name": "Cockpit3", "type": "n8n-nodes-base.cockpit", "typeVersion": 1, "position": [550, 350], "credentials": {"cockpitApi": {"id": "81", "name": "Cockpit API creds"}}, "id": "b1f38f40-c544-496f-bcb9-3957a755a497"}, {"parameters": {"resource": "form", "form": "FixedForm", "dataFieldsUi": {"field": [{"name": "name", "value": "=name{{Date.now()}}"}, {"name": "tag", "value": "=tag{{Date.now()}}"}]}}, "name": "Cockpit4", "type": "n8n-nodes-base.cockpit", "typeVersion": 1, "position": [550, 500], "credentials": {"cockpitApi": {"id": "81", "name": "Cockpit API creds"}}, "id": "9567077a-58f2-453e-8d36-35d4c68e9510"}], "connections": {"Cockpit": {"main": [[{"node": "Cockpit1", "type": "main", "index": 0}]]}, "Cockpit1": {"main": [[{"node": "Cockpit2", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "Cockpit", "type": "main", "index": 0}, {"node": "Cockpit3", "type": "main", "index": 0}, {"node": "Cockpit4", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}