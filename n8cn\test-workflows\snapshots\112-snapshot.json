{"data": {"startData": {}, "resultData": {"runData": {"Start": [{"startTime": 1710331886879, "executionTime": 0, "source": [], "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "RabbitMQ": [{"startTime": 1710331886879, "executionTime": 703, "source": [{"previousNode": "Start"}], "executionStatus": "success", "data": {"main": [[{"json": {"success": true}, "pairedItem": {"item": 0}}]]}}], "RabbitMQ1": [{"startTime": 1710331887582, "executionTime": 244, "source": [{"previousNode": "Start"}], "executionStatus": "success", "data": {"main": [[{"json": {"success": true}, "pairedItem": {"item": 0}}]]}}], "RabbitMQ2": [{"startTime": 1710331887826, "executionTime": 262, "source": [{"previousNode": "RabbitMQ1"}], "executionStatus": "success", "data": {"main": [[{"json": {"success": true}, "pairedItem": {"item": 0}}]]}}], "RabbitMQ3": [{"startTime": 1710331888088, "executionTime": 260, "source": [{"previousNode": "RabbitMQ2"}], "executionStatus": "success", "data": {"main": [[{"json": {"success": true}, "pairedItem": {"item": 0}}]]}}], "RabbitMQ4": [{"startTime": 1710331888349, "executionTime": 240, "source": [{"previousNode": "RabbitMQ3"}], "executionStatus": "success", "data": {"main": [[{"json": {"success": true}, "pairedItem": {"item": 0}}]]}}]}, "lastNodeExecuted": "RabbitMQ4"}, "executionData": {"contextData": {}, "nodeExecutionStack": [], "metadata": {}, "waitingExecution": {}, "waitingExecutionSource": {}}}, "mode": "cli", "startedAt": "2024-03-13T12:11:26.879Z", "stoppedAt": "2024-03-13T12:11:28.589Z", "status": "running", "finished": true}