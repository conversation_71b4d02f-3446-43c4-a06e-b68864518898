{"createdAt": "2021-02-19T09:08:47.547Z", "updatedAt": "2021-05-25T12:40:44.118Z", "id": "45", "name": "GoogleSheets:SpreadSheet:create:Sheet:create append read lookup update delete clean remove", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300], "id": "8c3c86be-d507-41b2-835b-9b49c1c34dc1"}, {"parameters": {"authentication": "serviceAccount", "resource": "spreadsheet", "title": "=TestSpreadSheet {{(new Date()).toGMTString()}}", "sheetsUi": {"sheetValues": []}, "options": {}}, "name": "Google Sheets", "type": "n8n-nodes-base.googleSheets", "typeVersion": 1, "position": [440, 200], "credentials": {"googleApi": {"id": "196", "name": "Google API creds"}}, "id": "ca817592-040a-4fa1-a171-abe44d7a011c"}, {"parameters": {"authentication": "serviceAccount", "operation": "append", "sheetId": "1Vp6KkHSquwxi2Y4xiloyUMFrTAg9VHJ-PhGdgYq4apE", "range": "A:B", "options": {"valueInputMode": "USER_ENTERED"}}, "name": "Google Sheets1", "type": "n8n-nodes-base.googleSheets", "typeVersion": 1, "position": [610, 360], "credentials": {"googleApi": {"id": "196", "name": "Google API creds"}}, "id": "f00b599c-3fd2-4c89-a9f0-84f33f4b0271"}, {"parameters": {"authentication": "serviceAccount", "operation": "update", "sheetId": "1Vp6KkHSquwxi2Y4xiloyUMFrTAg9VHJ-PhGdgYq4apE", "range": "Sheet1!A:B", "options": {"valueInputMode": "USER_ENTERED"}}, "name": "Google Sheets2", "type": "n8n-nodes-base.googleSheets", "typeVersion": 1, "position": [1200, 360], "credentials": {"googleApi": {"id": "196", "name": "Google API creds"}}, "id": "174fac97-78c0-4474-9f9e-2b0a154eb6c5"}, {"parameters": {"keepOnlySet": true, "values": {"number": [{"name": "id", "value": "={{$node[\"Function\"].json[\"id\"]}}"}], "string": [{"name": "name", "value": "=Updated {{$node[\"Function\"].json[\"name\"]}}"}]}, "options": {}}, "name": "Set1", "type": "n8n-nodes-base.set", "position": [1050, 360], "typeVersion": 1, "alwaysOutputData": false, "notesInFlow": true, "notes": "Update one item name", "id": "72366cc8-257d-40fe-be42-cc85b8a01edf"}, {"parameters": {"authentication": "serviceAccount", "sheetId": "1Vp6KkHSquwxi2Y4xiloyUMFrTAg9VHJ-PhGdgYq4apE", "range": "Sheet1!A:C", "options": {}}, "name": "Google Sheets3", "type": "n8n-nodes-base.googleSheets", "typeVersion": 1, "position": [770, 360], "credentials": {"googleApi": {"id": "196", "name": "Google API creds"}}, "id": "34ba96c0-f1d2-4d78-883c-f809166e8688"}, {"parameters": {"authentication": "serviceAccount", "operation": "lookup", "sheetId": "1Vp6KkHSquwxi2Y4xiloyUMFrTAg9VHJ-PhGdgYq4apE", "range": "Sheet1!A:B", "lookupColumn": "name", "lookupValue": "test1", "options": {}}, "name": "Google Sheets4", "type": "n8n-nodes-base.googleSheets", "typeVersion": 1, "position": [910, 360], "alwaysOutputData": true, "credentials": {"googleApi": {"id": "196", "name": "Google API creds"}}, "id": "e29d0550-1a22-4fc7-96a1-d4cc1b924b2f"}, {"parameters": {"authentication": "serviceAccount", "operation": "clear", "sheetId": "1Vp6KkHSquwxi2Y4xiloyUMFrTAg9VHJ-PhGdgYq4apE", "range": "A2:C50"}, "name": "Google Sheets5", "type": "n8n-nodes-base.googleSheets", "typeVersion": 1, "position": [1500, 360], "credentials": {"googleApi": {"id": "196", "name": "Google API creds"}}, "id": "6bbb5985-4367-4492-85db-aefcea813c99"}, {"parameters": {"authentication": "serviceAccount", "operation": "delete", "sheetId": "1Vp6KkHSquwxi2Y4xiloyUMFrTAg9VHJ-PhGdgYq4apE", "toDelete": {"columns": [], "rows": [{"sheetId": 0, "startIndex": 1}]}}, "name": "Google Sheets6", "type": "n8n-nodes-base.googleSheets", "typeVersion": 1, "position": [1350, 360], "credentials": {"googleApi": {"id": "196", "name": "Google API creds"}}, "id": "0c129d65-37c8-48a6-b0fa-062a1b474d96"}, {"parameters": {"functionCode": "items = [\n    {\n        json:{\n            id: Math.round(Math.random()*1000),\n            name: 'test1'\n        }   \n    },\n    {\n        json:{\n            id: Math.round(Math.random()*1000),\n            name: 'test2'\n        }   \n    }\n]\nreturn items;"}, "name": "Function", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [440, 360], "notesInFlow": true, "notes": "create the input data", "id": "e4dce6be-f388-4159-889f-0c0243a9a407"}, {"parameters": {"authentication": "serviceAccount", "operation": "create", "sheetId": "={{$node[\"Google Sheets\"].json[\"spreadsheetId\"]}}", "options": {"gridProperties": {"columnCount": 10, "rowCount": 20}, "rightToLeft": false, "index": 1, "tabColor": "#FF6E39"}}, "name": "Google Sheets7", "type": "n8n-nodes-base.googleSheets", "typeVersion": 1, "position": [600, 200], "credentials": {"googleApi": {"id": "196", "name": "Google API creds"}}, "id": "e32770bf-a9e6-47cf-a75f-6e335ca46d3d"}, {"parameters": {"authentication": "serviceAccount", "operation": "remove", "sheetId": "={{$node[\"Google Sheets\"].json[\"spreadsheetId\"]}}", "id": "={{$node[\"Google Sheets7\"].json[\"sheetId\"]}}"}, "name": "Google Sheets8", "type": "n8n-nodes-base.googleSheets", "typeVersion": 1, "position": [750, 200], "credentials": {"googleApi": {"id": "196", "name": "Google API creds"}}, "id": "b3ff83fc-5bb3-4472-bbf4-c24a56e4ecf1"}], "connections": {"Start": {"main": [[{"node": "Google Sheets", "type": "main", "index": 0}, {"node": "Function", "type": "main", "index": 0}]]}, "Google Sheets1": {"main": [[{"node": "Google Sheets3", "type": "main", "index": 0}]]}, "Google Sheets2": {"main": [[{"node": "Google Sheets6", "type": "main", "index": 0}]]}, "Set1": {"main": [[{"node": "Google Sheets2", "type": "main", "index": 0}]]}, "Google Sheets3": {"main": [[{"node": "Google Sheets4", "type": "main", "index": 0}]]}, "Google Sheets4": {"main": [[{"node": "Set1", "type": "main", "index": 0}]]}, "Google Sheets6": {"main": [[{"node": "Google Sheets5", "type": "main", "index": 0}]]}, "Function": {"main": [[{"node": "Google Sheets1", "type": "main", "index": 0}]]}, "Google Sheets": {"main": [[{"node": "Google Sheets7", "type": "main", "index": 0}]]}, "Google Sheets7": {"main": [[{"node": "Google Sheets8", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}