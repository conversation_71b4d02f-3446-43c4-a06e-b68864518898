{"data": {"startData": {}, "resultData": {"runData": {"Start": [{"startTime": 1747344011734, "executionIndex": 0, "source": [], "hints": [], "executionTime": 0, "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "Date & Time": [{"startTime": 1747344011734, "executionIndex": 1, "source": [{"previousNode": "Start"}], "hints": [], "executionTime": 0, "executionStatus": "success", "data": {"main": [[{"json": {"data": "03/03/2021"}, "pairedItem": {"item": 0}}]]}}], "Date & Time1": [{"startTime": 1747344011734, "executionIndex": 2, "source": [{"previousNode": "Date & Time"}], "hints": [], "executionTime": 0, "executionStatus": "success", "data": {"main": [[{"json": {"data": "2021/03/03"}, "pairedItem": {"item": 0}}]]}}], "Date & Time2": [{"startTime": 1747344011735, "executionIndex": 3, "source": [{"previousNode": "Date & Time1"}], "hints": [], "executionTime": 0, "executionStatus": "success", "data": {"main": [[{"json": {"data": "March 03 2021"}, "pairedItem": {"item": 0}}]]}}], "Date & Time3": [{"startTime": 1747344011735, "executionIndex": 4, "source": [{"previousNode": "Date & Time2"}], "hints": [], "executionTime": 0, "executionStatus": "success", "data": {"main": [[{"json": {"data": "03-03-2021"}, "pairedItem": {"item": 0}}]]}}], "Date & Time4": [{"startTime": 1747344011735, "executionIndex": 5, "source": [{"previousNode": "Date & Time3"}], "hints": [], "executionTime": 0, "executionStatus": "success", "data": {"main": [[{"json": {"data": "2021-03-03"}, "pairedItem": {"item": 0}}]]}}], "Date & Time5": [{"startTime": 1747344011735, "executionIndex": 6, "source": [{"previousNode": "Date & Time4"}], "hints": [], "executionTime": 0, "executionStatus": "success", "data": {"main": [[{"json": {"data": "1614764492"}, "pairedItem": {"item": 0}}]]}}], "Function": [{"startTime": 1747344011735, "executionIndex": 7, "source": [{"previousNode": "Date & Time5"}], "hints": [], "executionTime": 2, "executionStatus": "success", "data": {"main": [[{"json": {"data": "1614764492"}, "pairedItem": {"item": 0}, "index": 0}]]}}]}, "lastNodeExecuted": "Function"}, "executionData": {"contextData": {}, "nodeExecutionStack": [], "metadata": {}, "waitingExecution": {}, "waitingExecutionSource": {}}}, "mode": "cli", "startedAt": "2025-05-15T21:20:11.734Z", "stoppedAt": "2025-05-15T21:20:11.737Z", "status": "running", "finished": true}