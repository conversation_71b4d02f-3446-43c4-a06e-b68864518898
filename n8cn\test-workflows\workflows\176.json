{"createdAt": "2021-04-20T13:25:03.086Z", "updatedAt": "2021-04-20T15:03:32.741Z", "id": "176", "name": "uProc:Audio:*:company:*;", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300], "id": "d0c17f4b-8961-440e-a16c-c2b24573ded4"}, {"parameters": {"group": "audio", "text": "n8n rocks!", "gender": "female", "language": "american", "additionalOptions": {}}, "name": "uProc", "type": "n8n-nodes-base.uproc", "typeVersion": 1, "position": [560, 200], "credentials": {"uprocApi": {"id": "142", "name": "uProc API creds"}}, "id": "93760945-e663-434d-929b-bde008a9048d"}, {"parameters": {"group": "audio", "tool": "getAudioSpeechByText", "text": "n8n rocks!", "gender": "female", "language": "american", "additionalOptions": {}}, "name": "uProc1", "type": "n8n-nodes-base.uproc", "typeVersion": 1, "position": [740, 200], "credentials": {"uprocApi": {"id": "142", "name": "uProc API creds"}}, "id": "048ecc11-0c05-474f-b05c-758e4ed5debb"}, {"parameters": {"group": "company", "tool": "getCifNormalized", "cif": "B 12345 678", "additionalOptions": {}}, "name": "uProc10", "type": "n8n-nodes-base.uproc", "typeVersion": 1, "position": [560, 350], "credentials": {"uprocApi": {"id": "142", "name": "uProc API creds"}}, "id": "a6e61a99-e759-45ce-9564-c07e9edd4ff9"}, {"parameters": {"group": "company", "cif": "B 12345 678", "additionalOptions": {}}, "name": "uProc11", "type": "n8n-nodes-base.uproc", "typeVersion": 1, "position": [690, 350], "credentials": {"uprocApi": {"id": "142", "name": "uProc API creds"}}, "id": "3176fdab-d652-4402-883f-eb3b49173235"}, {"parameters": {"group": "company", "tool": "checkNumberIsin", "isin": "US0004026250", "additionalOptions": {}}, "name": "uProc12", "type": "n8n-nodes-base.uproc", "typeVersion": 1, "position": [820, 350], "credentials": {"uprocApi": {"id": "142", "name": "uProc API creds"}}, "id": "e015d1d9-aa1e-49e5-823c-9e6372d316ec"}, {"parameters": {"group": "company", "tool": "checkNumberSsEs", "number": "998239812282", "additionalOptions": {}}, "name": "uProc13", "type": "n8n-nodes-base.uproc", "typeVersion": 1, "position": [950, 350], "credentials": {"uprocApi": {"id": "142", "name": "uProc API creds"}}, "id": "90bde607-e419-4547-b8e2-e96a181dd152"}, {"parameters": {"group": "company", "tool": "getCompanyByCif", "cif": "B 12345 678", "additionalOptions": {}}, "name": "uProc14", "type": "n8n-nodes-base.uproc", "typeVersion": 1, "position": [1080, 350], "credentials": {"uprocApi": {"id": "142", "name": "uProc API creds"}}, "id": "d4ad8ce2-d306-432d-a6de-a82826964eda"}, {"parameters": {"group": "company", "tool": "getCompanyByDomain", "domain": "n8n.io", "additionalOptions": {}}, "name": "uProc15", "type": "n8n-nodes-base.uproc", "typeVersion": 1, "position": [1210, 350], "credentials": {"uprocApi": {"id": "142", "name": "uProc API creds"}}, "id": "d2852025-5de5-41fa-9f29-d9783cb3ec9d"}, {"parameters": {"group": "company", "tool": "getCompanyByDuns", "duns": "*********", "additionalOptions": {}}, "name": "uProc16", "type": "n8n-nodes-base.uproc", "typeVersion": 1, "position": [1340, 350], "credentials": {"uprocApi": {"id": "142", "name": "uProc API creds"}}, "id": "88e58d49-0c18-42e4-84bf-d9752cf74fa7"}, {"parameters": {"group": "company", "tool": "getCompanyByEmail", "email": "<EMAIL>", "additionalOptions": {}}, "name": "uProc17", "type": "n8n-nodes-base.uproc", "typeVersion": 1, "position": [1470, 350], "credentials": {"uprocApi": {"id": "142", "name": "uProc API creds"}}, "id": "74543b06-f296-417a-a9cb-cf5c9aeb8bf4"}, {"parameters": {"group": "company", "tool": "getCompanyByIp", "ip": "***********", "additionalOptions": {}}, "name": "uProc18", "type": "n8n-nodes-base.uproc", "typeVersion": 1, "position": [1600, 350], "credentials": {"uprocApi": {"id": "142", "name": "uProc API creds"}}, "id": "6cbbc927-2cef-4688-9824-50e15a2ab433"}, {"parameters": {"group": "company", "tool": "getCompanyByName", "country": "DE", "name": "n8n", "additionalOptions": {}}, "name": "uProc19", "type": "n8n-nodes-base.uproc", "typeVersion": 1, "position": [1730, 350], "credentials": {"uprocApi": {"id": "142", "name": "uProc API creds"}}, "id": "b9198acf-94fb-4d58-81f6-9713d3588e64"}, {"parameters": {"group": "company", "tool": "getCompanyByPhone", "phone": "*********", "additionalOptions": {}}, "name": "uProc20", "type": "n8n-nodes-base.uproc", "typeVersion": 1, "position": [1860, 350], "credentials": {"uprocApi": {"id": "142", "name": "uProc API creds"}}, "id": "2862f234-cc12-4df0-b994-26d38ee16414"}, {"parameters": {"group": "company", "tool": "getCompanyByProfile", "url": "https://twitter.com/n8n_io", "additionalOptions": {}}, "name": "uProc21", "type": "n8n-nodes-base.uproc", "typeVersion": 1, "position": [1990, 350], "credentials": {"uprocApi": {"id": "142", "name": "uProc API creds"}}, "id": "b6c61930-8b49-4c7d-8831-93722da0f894"}, {"parameters": {"group": "company", "tool": "getRoleClassified", "role": "Junior web developer", "additionalOptions": {}}, "name": "uProc22", "type": "n8n-nodes-base.uproc", "typeVersion": 1, "position": [2120, 350], "credentials": {"uprocApi": {"id": "142", "name": "uProc API creds"}}, "id": "d051cee8-5bca-4c2a-b945-95cbf54a08b4"}, {"parameters": {"group": "company", "tool": "checkCompanyDebtorByTaxid", "taxid": "B12345678", "additionalOptions": {}}, "name": "uProc23", "type": "n8n-nodes-base.uproc", "typeVersion": 1, "position": [2250, 350], "credentials": {"uprocApi": {"id": "142", "name": "uProc API creds"}}, "id": "421b79d2-8337-41f9-9f3b-e44d49040cb3"}, {"parameters": {"group": "company", "tool": "getPersonDecisionMaker", "company": "n8n.io", "area": "Engineering", "additionalOptions": {}}, "name": "uProc24", "type": "n8n-nodes-base.uproc", "typeVersion": 1, "position": [2380, 350], "credentials": {"uprocApi": {"id": "142", "name": "uProc API creds"}}, "id": "1e955a1c-eb87-4c24-af10-8f5bfc0728dd"}, {"parameters": {"group": "company", "tool": "getPersonDecisionMakerBySearch", "company": "n8n.io", "area": "Product", "clevel": "No", "additionalOptions": {}}, "name": "uProc25", "type": "n8n-nodes-base.uproc", "typeVersion": 1, "position": [2510, 350], "credentials": {"uprocApi": {"id": "142", "name": "uProc API creds"}}, "id": "d288cae2-b1d9-46e7-a4f2-339fc7a8fab1"}, {"parameters": {"group": "company", "tool": "getCompanyDomainByName", "name": "n8n.io", "additionalOptions": {}}, "name": "uProc26", "type": "n8n-nodes-base.uproc", "typeVersion": 1, "position": [2640, 350], "credentials": {"uprocApi": {"id": "142", "name": "uProc API creds"}}, "id": "896d6a19-4717-4e1e-9380-b7fa88e05a3b"}, {"parameters": {"group": "company", "tool": "getPersonEmailsByDomainAndArea", "domain": "n8n.io", "area": "Engineering", "additionalOptions": {}}, "name": "uProc27", "type": "n8n-nodes-base.uproc", "typeVersion": 1, "position": [2770, 350], "credentials": {"uprocApi": {"id": "142", "name": "uProc API creds"}}, "id": "c5b64761-444e-4ee1-8289-b3e3805b071d"}, {"parameters": {"group": "company", "tool": "getCompanyExtendedByDomain", "domain": "n8n.io", "additionalOptions": {}}, "name": "uProc28", "type": "n8n-nodes-base.uproc", "typeVersion": 1, "position": [2900, 350], "credentials": {"uprocApi": {"id": "142", "name": "uProc API creds"}}, "id": "01cb8555-31bc-49ec-82a2-36b24bb48873"}, {"parameters": {"group": "company", "tool": "getCompanyExtendedByEmail", "email": "<EMAIL>", "additionalOptions": {}}, "name": "uProc29", "type": "n8n-nodes-base.uproc", "typeVersion": 1, "position": [3030, 350], "credentials": {"uprocApi": {"id": "142", "name": "uProc API creds"}}, "id": "a801f605-1677-443b-8c86-fe636414c2ab"}, {"parameters": {"group": "company", "tool": "getProfileFacebookByCompany", "company": "n8n.io", "additionalOptions": {}}, "name": "uProc30", "type": "n8n-nodes-base.uproc", "typeVersion": 1, "position": [3160, 350], "credentials": {"uprocApi": {"id": "142", "name": "uProc API creds"}}, "id": "a0f7131d-c121-4eb2-b6f5-bf72e3af23c0"}, {"parameters": {"group": "company", "tool": "getCompanyFinancialByDomain", "domain": "n8n.io", "additionalOptions": {}}, "name": "uProc31", "type": "n8n-nodes-base.uproc", "typeVersion": 1, "position": [3290, 350], "credentials": {"uprocApi": {"id": "142", "name": "uProc API creds"}}, "id": "1b027a55-7122-4a63-8364-9dd7176f9672"}, {"parameters": {"group": "company", "tool": "getCompanyFinancialByDuns", "duns": "*********", "additionalOptions": {}}, "name": "uProc32", "type": "n8n-nodes-base.uproc", "typeVersion": 1, "position": [3420, 350], "credentials": {"uprocApi": {"id": "142", "name": "uProc API creds"}}, "id": "b046e6e6-34c2-4b91-ab6b-0f69905f85ce"}, {"parameters": {"group": "company", "tool": "getCompanyFinancialByName", "name": "n8n.io", "additionalOptions": {}}, "name": "uProc33", "type": "n8n-nodes-base.uproc", "typeVersion": 1, "position": [3550, 350], "credentials": {"uprocApi": {"id": "142", "name": "uProc API creds"}}, "id": "a01a0c27-1386-45a7-a86e-8e118bf5d327"}, {"parameters": {"group": "company", "tool": "getCompanyFinancialByTaxid", "taxid": "B12345678", "additionalOptions": {}}, "name": "uProc34", "type": "n8n-nodes-base.uproc", "typeVersion": 1, "position": [3680, 350], "credentials": {"uprocApi": {"id": "142", "name": "uProc API creds"}}, "id": "60c8d394-358a-47e1-9a2c-dd11553737a0"}, {"parameters": {"group": "company", "tool": "getCompanyGeocodedByIp", "ip": "***********", "additionalOptions": {}}, "name": "uProc35", "type": "n8n-nodes-base.uproc", "typeVersion": 1, "position": [3810, 350], "credentials": {"uprocApi": {"id": "142", "name": "uProc API creds"}}, "id": "a4efe1ed-b5da-4185-99e0-840da0544e4f"}, {"parameters": {"group": "company", "tool": "getProfileLinkedinByCompany", "company": "n8n.io", "additionalOptions": {}}, "name": "uProc36", "type": "n8n-nodes-base.uproc", "typeVersion": 1, "position": [3940, 350], "credentials": {"uprocApi": {"id": "142", "name": "uProc API creds"}}, "id": "ad662983-c0b8-4650-9577-dd23ac9b3db7"}, {"parameters": {"group": "company", "tool": "getPersonListByParams", "country": "DE", "phone": "*********", "email": "<EMAIL>", "company": "n8n.io", "area": "Information technology", "seniority": "Intermediate", "additionalOptions": {}}, "name": "uProc37", "type": "n8n-nodes-base.uproc", "typeVersion": 1, "position": [4070, 350], "credentials": {"uprocApi": {"id": "142", "name": "uProc API creds"}}, "id": "aaaa41b2-d90e-4cb4-8083-87cd7bdcaf69"}, {"parameters": {"group": "company", "tool": "getPersonMultipleDecisionMakerBySearch", "company": "n8n.io", "area": "Information technology", "clevel": "No", "additionalOptions": {}}, "name": "uProc38", "type": "n8n-nodes-base.uproc", "typeVersion": 1, "position": [4200, 350], "credentials": {"uprocApi": {"id": "142", "name": "uProc API creds"}}, "id": "ba76171b-5070-4938-9e9d-923121f1c45a"}, {"parameters": {"group": "company", "tool": "getCompanyNameByDomain", "domain": "n8n.io", "additionalOptions": {}}, "name": "uProc39", "type": "n8n-nodes-base.uproc", "typeVersion": 1, "position": [4330, 350], "credentials": {"uprocApi": {"id": "142", "name": "uProc API creds"}}, "id": "d76f3d0a-9a82-4d93-a783-8f793c412c94"}, {"parameters": {"group": "company", "tool": "getCompanyPhoneByDomain", "domain": "n8n.io", "additionalOptions": {}}, "name": "uProc40", "type": "n8n-nodes-base.uproc", "typeVersion": 1, "position": [4460, 350], "credentials": {"uprocApi": {"id": "142", "name": "uProc API creds"}}, "id": "f53778bf-935f-4f49-9281-552993ae1429"}, {"parameters": {"group": "company", "tool": "getCompanyPhoneByName", "name": "n8n.io", "additionalOptions": {}}, "name": "uProc41", "type": "n8n-nodes-base.uproc", "typeVersion": 1, "position": [4590, 350], "credentials": {"uprocApi": {"id": "142", "name": "uProc API creds"}}, "id": "ab0ec7c3-397d-44de-b082-33e244ba1272"}, {"parameters": {"group": "company", "tool": "getProfileTwitterByCompany", "company": "n8n.io", "additionalOptions": {}}, "name": "uProc42", "type": "n8n-nodes-base.uproc", "typeVersion": 1, "position": [4720, 350], "credentials": {"uprocApi": {"id": "142", "name": "uProc API creds"}}, "id": "bbe5a143-20db-49b7-a9ac-8600a895b74d"}], "connections": {"uProc": {"main": [[{"node": "uProc1", "type": "main", "index": 0}]]}, "uProc10": {"main": [[{"node": "uProc11", "type": "main", "index": 0}]]}, "uProc11": {"main": [[{"node": "uProc12", "type": "main", "index": 0}]]}, "uProc12": {"main": [[{"node": "uProc13", "type": "main", "index": 0}]]}, "uProc13": {"main": [[{"node": "uProc14", "type": "main", "index": 0}]]}, "uProc14": {"main": [[{"node": "uProc15", "type": "main", "index": 0}]]}, "uProc15": {"main": [[{"node": "uProc16", "type": "main", "index": 0}]]}, "uProc16": {"main": [[{"node": "uProc17", "type": "main", "index": 0}]]}, "uProc17": {"main": [[{"node": "uProc18", "type": "main", "index": 0}]]}, "uProc18": {"main": [[{"node": "uProc19", "type": "main", "index": 0}]]}, "uProc19": {"main": [[{"node": "uProc20", "type": "main", "index": 0}]]}, "uProc20": {"main": [[{"node": "uProc21", "type": "main", "index": 0}]]}, "uProc21": {"main": [[{"node": "uProc22", "type": "main", "index": 0}]]}, "uProc22": {"main": [[{"node": "uProc23", "type": "main", "index": 0}]]}, "uProc23": {"main": [[{"node": "uProc24", "type": "main", "index": 0}]]}, "uProc24": {"main": [[{"node": "uProc25", "type": "main", "index": 0}]]}, "uProc25": {"main": [[{"node": "uProc26", "type": "main", "index": 0}]]}, "uProc26": {"main": [[{"node": "uProc27", "type": "main", "index": 0}]]}, "uProc27": {"main": [[{"node": "uProc28", "type": "main", "index": 0}]]}, "uProc28": {"main": [[{"node": "uProc29", "type": "main", "index": 0}]]}, "uProc29": {"main": [[{"node": "uProc30", "type": "main", "index": 0}]]}, "uProc30": {"main": [[{"node": "uProc31", "type": "main", "index": 0}]]}, "uProc31": {"main": [[{"node": "uProc32", "type": "main", "index": 0}]]}, "uProc32": {"main": [[{"node": "uProc33", "type": "main", "index": 0}]]}, "uProc33": {"main": [[{"node": "uProc34", "type": "main", "index": 0}]]}, "uProc34": {"main": [[{"node": "uProc35", "type": "main", "index": 0}]]}, "uProc35": {"main": [[{"node": "uProc36", "type": "main", "index": 0}]]}, "uProc36": {"main": [[{"node": "uProc37", "type": "main", "index": 0}]]}, "uProc37": {"main": [[{"node": "uProc38", "type": "main", "index": 0}]]}, "uProc38": {"main": [[{"node": "uProc39", "type": "main", "index": 0}]]}, "uProc39": {"main": [[{"node": "uProc40", "type": "main", "index": 0}]]}, "uProc40": {"main": [[{"node": "uProc41", "type": "main", "index": 0}]]}, "uProc41": {"main": [[{"node": "uProc42", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "uProc", "type": "main", "index": 0}, {"node": "uProc10", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}