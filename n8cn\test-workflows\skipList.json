[{"workflowId": "1", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "4", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "5", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "10", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "19", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "20", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "21", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "22", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "26", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "27", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "28", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "29", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "30", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "31", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "33", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "34", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "38", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "39", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "40", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "41", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "42", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "43", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "45", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "46", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "47", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "49", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "50", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "51", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "54", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "56", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "57", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "59", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "60", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "64", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "65", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "66", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "68", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "69", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "72", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "73", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "74", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "75", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "76", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "77", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "78", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "79", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "80", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "82", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "85", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "89", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "92", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "94", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "102", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "104", "status": "SKIPPED", "skipReason": "This node is deprecated and has been replaced by the Extract From File node. Also it didn't interact with any live services, it could have been an integation test.", "ticketReference": ""}, {"workflowId": "106", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "109", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "110", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "112", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "113", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "115", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "116", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "117", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "118", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "119", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "120", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "121", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "122", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "123", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "124", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "125", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "126", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "127", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "128", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "129", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "130", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "131", "status": "SKIPPED", "skipReason": "This API service has been discontinued. For more details, please check here: https://notify-bot.line.me/closing-announce on node Line", "ticketReference": ""}, {"workflowId": "134", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "133", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "135", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "136", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "137", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "138", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "139", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "141", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "142", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "144", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "145", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "146", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "147", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "148", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "149", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "151", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "157", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "158", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "159", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "160", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "163", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "164", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "165", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "167", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "168", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "169", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "170", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "171", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "173", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "176", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "177", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "179", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "180", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "183", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "184", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "185", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "186", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "187", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "188", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "189", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "190", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "191", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "192", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "193", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "194", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "196", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "197", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "198", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "199", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "200", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "201", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "202", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "204", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "206", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "207", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "208", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "214", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "215", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "217", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "218", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "219", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "220", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "221", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "222", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "224", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "225", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "226", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "227", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "252", "status": "SKIPPED", "skipReason": "", "ticketReference": ""}, {"workflowId": "223", "status": "SKIPPED", "skipReason": "You can not complete this operation. Please check your balance on node LingvaNex", "ticketReference": ""}, {"workflowId": "143", "status": "SKIPPED", "skipReason": "Invalid API key for Clearbit.", "ticketReference": ""}, {"workflowId": "86", "status": "SKIPPED", "skipReason": "It looks like a timing issue with the delete operation. The contact was created but not found when trying to delete it. Tested it with the same workflow and it worked.", "ticketReference": "CAT-790"}, {"workflowId": "87", "status": "SKIPPED", "skipReason": "It looks like a timing issue with the delete operation. The contact was created but not found when trying to delete it. Tested it with the same workflow and it worked.", "ticketReference": "CAT-790"}, {"workflowId": "88", "status": "SKIPPED", "skipReason": "It looks like a timing issue with the delete operation. The contact was created but not found when trying to delete it. Tested it with the same workflow and it worked.", "ticketReference": "CAT-790"}, {"workflowId": "233", "status": "SKIPPED", "skipReason": "Not Found on node Qdrant Vector Store. Could be a timing issue.", "ticketReference": ""}, {"workflowId": "241", "status": "SKIPPED", "skipReason": "OpenAIAgent requires an OpenAI chat model on node AI Agent1", "ticketReference": ""}, {"workflowId": "259", "status": "SKIPPED", "skipReason": "Please return an array of objects, one for each item you would like to output. on node Code", "ticketReference": ""}, {"workflowId": "254", "status": "SKIPPED", "skipReason": "Overloaded on node AI Agent3. Is this because I ran the test workflow multiple times?", "ticketReference": ""}]