diff --git a/index.js b/index.js
index b054bc7cbe742e8948e62dc3fdbaba9080e4aee1..7327ad02b48b3746cba93e0317ed190e61886888 100644
--- a/index.js
+++ b/index.js
@@ -7,7 +7,7 @@ const { findUp } = require('./findUp');
 const found = findUp('.npmrc', __dirname);
 const rootDir = path.dirname(found);
 
-const localRulesPath = path.resolve(rootDir, 'packages', '@n8n_io', 'eslint-config', 'local-rules.js')
+const localRulesPath = path.resolve(rootDir, 'packages', '@n8n', 'eslint-config', 'local-rules.js')
 const rules = require(localRulesPath);
 
 if (!rules) {
