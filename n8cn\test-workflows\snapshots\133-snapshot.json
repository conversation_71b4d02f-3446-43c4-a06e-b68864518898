{"data": {"startData": {}, "resultData": {"runData": {"Start": [{"startTime": *************, "executionTime": 0, "source": [], "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "Twilio": [{"startTime": *************, "executionTime": 661, "source": [{"previousNode": "Start"}], "executionStatus": "success", "data": {"main": [[{"json": {"body": "Message1710331887805", "num_segments": "1", "direction": "outbound-api", "from": "+***********", "date_updated": "Wed, 13 Mar 2024 12:11:28 +0000", "price": {"object": true}, "error_message": {"object": true}, "uri": "/2010-04-01/Accounts/AC8dd0c6e08b0f196d34adde4f00507d9f/Messages/SMd99c4e5b501391cb4fbe91937f23ec3b.json", "account_sid": "AC8dd0c6e08b0f196d34adde4f00507d9f", "num_media": "0", "to": "+***********", "date_created": "Wed, 13 Mar 2024 12:11:28 +0000", "status": "queued", "sid": "SMd99c4e5b501391cb4fbe91937f23ec3b", "date_sent": {"object": true}, "messaging_service_sid": {"object": true}, "error_code": {"object": true}, "price_unit": "USD", "api_version": "2010-04-01", "subresource_uris": {"object": true}}, "pairedItem": {"item": 0}}]]}}]}, "lastNodeExecuted": "<PERSON><PERSON><PERSON>"}, "executionData": {"contextData": {}, "nodeExecutionStack": [], "metadata": {}, "waitingExecution": {}, "waitingExecutionSource": {}}}, "mode": "cli", "startedAt": "2024-03-13T12:11:27.804Z", "stoppedAt": "2024-03-13T12:11:28.465Z", "status": "running", "finished": true}