{"createdAt": "2021-05-17T15:09:40.220Z", "updatedAt": "2021-05-18T07:57:05.962Z", "id": "206", "name": "Keap:Company:create getAll:Contact:upsert get getAll delete:ContactNote:create get update getAll delete:ContactTag:create getAll delete", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300], "id": "9489a524-46de-44e4-b4f9-e5ff8202cc3e"}, {"parameters": {"companyName": "=Company_{{(new Date).toISOString()}}", "additionalFields": {}}, "name": "Keap", "type": "n8n-nodes-base.keap", "typeVersion": 1, "position": [600, 200], "credentials": {"keapOAuth2Api": {"id": "178", "name": "Keap OAuth2 API creds"}}, "id": "690b0194-0a2a-41a3-a6bd-b67ce0345f42"}, {"parameters": {"operation": "getAll", "limit": 1, "options": {}}, "name": "Keap1", "type": "n8n-nodes-base.keap", "typeVersion": 1, "position": [750, 200], "credentials": {"keapOAuth2Api": {"id": "178", "name": "Keap OAuth2 API creds"}}, "id": "07a5aca1-73a9-4812-9a35-0e62d814c540"}, {"parameters": {"resource": "contact", "additionalFields": {"companyId": "={{$node[\"Keap\"].json[\"id\"]}}"}, "addressesUi": {"addressesValues": []}, "emailsUi": {"emailsValues": [{"field": "EMAIL1", "email": "=fakeemail{{Date.now()}}@test.com"}]}}, "name": "Keap2", "type": "n8n-nodes-base.keap", "typeVersion": 1, "position": [750, 350], "credentials": {"keapOAuth2Api": {"id": "178", "name": "Keap OAuth2 API creds"}}, "id": "44871399-7e52-4d49-9090-32a07fc55440"}, {"parameters": {"resource": "contact", "operation": "get", "contactId": "={{$node[\"Keap2\"].json[\"id\"]}}", "options": {}}, "name": "Keap3", "type": "n8n-nodes-base.keap", "typeVersion": 1, "position": [900, 350], "credentials": {"keapOAuth2Api": {"id": "178", "name": "Keap OAuth2 API creds"}}, "id": "17a7232c-8cc9-448c-a2c0-bd1084fdd771"}, {"parameters": {"resource": "contact", "operation": "getAll", "limit": 1, "options": {}}, "name": "Keap4", "type": "n8n-nodes-base.keap", "typeVersion": 1, "position": [1040, 350], "credentials": {"keapOAuth2Api": {"id": "178", "name": "Keap OAuth2 API creds"}}, "id": "7007df2b-c66a-4a7b-b690-9d0124e73357"}, {"parameters": {"resource": "contact", "operation": "delete", "contactId": "={{$node[\"Keap2\"].json[\"id\"]}}"}, "name": "Keap5", "type": "n8n-nodes-base.keap", "typeVersion": 1, "position": [1200, 350], "credentials": {"keapOAuth2Api": {"id": "178", "name": "Keap OAuth2 API creds"}}, "id": "f61df6f5-ab1c-4344-8ab5-8f09ceddbe8b"}, {"parameters": {"resource": "contactNote", "userId": 1, "contactId": "={{$node[\"Keap2\"].json[\"id\"]}}", "additionalFields": {"body": "", "title": "=Note{Date.now()}}"}}, "name": "Keap6", "type": "n8n-nodes-base.keap", "typeVersion": 1, "position": [900, 510], "credentials": {"keapOAuth2Api": {"id": "178", "name": "Keap OAuth2 API creds"}}, "id": "1af7e3bf-9135-4ffe-a875-b6ae453a44b1"}, {"parameters": {"resource": "contactNote", "operation": "get", "noteId": "={{$node[\"Keap6\"].json[\"id\"]}}"}, "name": "Keap7", "type": "n8n-nodes-base.keap", "typeVersion": 1, "position": [1050, 510], "credentials": {"keapOAuth2Api": {"id": "178", "name": "Keap OAuth2 API creds"}}, "id": "d8252291-bbf9-4ee1-a9d4-093ae967ef12"}, {"parameters": {"resource": "contactNote", "operation": "update", "noteId": "={{$node[\"Keap6\"].json[\"id\"]}}", "additionalFields": {"title": "Updated Title"}}, "name": "Keap8", "type": "n8n-nodes-base.keap", "typeVersion": 1, "position": [1200, 510], "credentials": {"keapOAuth2Api": {"id": "178", "name": "Keap OAuth2 API creds"}}, "id": "54756d28-a79f-4427-8d5d-5c2d80ac5b17"}, {"parameters": {"resource": "contactNote", "operation": "getAll", "limit": 1, "filters": {}}, "name": "Keap9", "type": "n8n-nodes-base.keap", "typeVersion": 1, "position": [1360, 510], "credentials": {"keapOAuth2Api": {"id": "178", "name": "Keap OAuth2 API creds"}}, "id": "81bdce10-1de6-4d11-8624-1a0516931e49"}, {"parameters": {"resource": "contactNote", "operation": "delete", "noteId": "={{$node[\"Keap6\"].json[\"id\"]}}"}, "name": "Keap10", "type": "n8n-nodes-base.keap", "typeVersion": 1, "position": [1500, 510], "credentials": {"keapOAuth2Api": {"id": "178", "name": "Keap OAuth2 API creds"}}, "id": "a8cb848a-36d8-4fb6-b845-37674c6c7733"}, {"parameters": {"resource": "contactTag", "contactId": "={{$node[\"Keap2\"].json[\"id\"]}}", "tagIds": [93]}, "name": "Keap11", "type": "n8n-nodes-base.keap", "typeVersion": 1, "position": [900, 660], "credentials": {"keapOAuth2Api": {"id": "178", "name": "Keap OAuth2 API creds"}}, "id": "c81d1109-4fbb-4dde-afe4-386710f9f624"}, {"parameters": {"resource": "contactTag", "operation": "getAll", "contactId": "={{$node[\"Keap2\"].json[\"id\"]}}", "limit": 1}, "name": "Keap12", "type": "n8n-nodes-base.keap", "typeVersion": 1, "position": [1050, 660], "credentials": {"keapOAuth2Api": {"id": "178", "name": "Keap OAuth2 API creds"}}, "id": "b0c03850-0004-4680-9da0-9ea9ded1a525"}, {"parameters": {"resource": "contactTag", "operation": "delete", "contactId": "={{$node[\"Keap2\"].json[\"id\"]}}", "tagIds": "93,"}, "name": "Keap13", "type": "n8n-nodes-base.keap", "typeVersion": 1, "position": [1190, 660], "credentials": {"keapOAuth2Api": {"id": "178", "name": "Keap OAuth2 API creds"}}, "id": "311e758c-48f7-4738-862d-b34299ac6d08"}], "connections": {"Keap": {"main": [[{"node": "Keap1", "type": "main", "index": 0}, {"node": "Keap2", "type": "main", "index": 0}]]}, "Keap2": {"main": [[{"node": "Keap6", "type": "main", "index": 0}, {"node": "Keap11", "type": "main", "index": 0}]]}, "Keap3": {"main": [[{"node": "Keap4", "type": "main", "index": 0}]]}, "Keap4": {"main": [[{"node": "Keap5", "type": "main", "index": 0}]]}, "Keap6": {"main": [[{"node": "Keap7", "type": "main", "index": 0}]]}, "Keap7": {"main": [[{"node": "Keap8", "type": "main", "index": 0}]]}, "Keap8": {"main": [[{"node": "Keap9", "type": "main", "index": 0}]]}, "Keap9": {"main": [[{"node": "Keap10", "type": "main", "index": 0}]]}, "Keap10": {"main": [[{"node": "Keap3", "type": "main", "index": 0}]]}, "Keap11": {"main": [[{"node": "Keap12", "type": "main", "index": 0}]]}, "Keap12": {"main": [[{"node": "Keap13", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "Keap", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}