{"data": {"startData": {}, "resultData": {"runData": {"Start": [{"startTime": 1747344004590, "executionIndex": 0, "source": [], "hints": [], "executionTime": 0, "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "Coda11": [{"startTime": 1747344004591, "executionIndex": 1, "source": [{"previousNode": "Start"}], "hints": [], "executionTime": 2921, "executionStatus": "success", "data": {"main": [[{"json": {"id": "table-K1_z3jl_HH", "type": "table", "tableType": "view", "href": "https://coda.io/apis/v1/docs/2-5DWWeFZp/tables/table-K1_z3jl_HH", "browserLink": "https://coda.io/d/_d2-5DWWeFZp#_tutable-K1_z3jl_HH", "name": "Table View", "parent": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "Coda4": [{"startTime": 1747344007512, "executionIndex": 2, "source": [{"previousNode": "Start"}], "hints": [], "executionTime": 1160, "executionStatus": "success", "data": {"main": [[{"json": {"id": "c-XvglOYNTU8", "type": "column", "name": "number", "href": "https://coda.io/apis/v1/docs/2-5DWWeFZp/tables/grid-lDqTU2W4nP/columns/c-XvglOYNTU8", "display": true, "format": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "Coda2": [{"startTime": 1747344008672, "executionIndex": 3, "source": [{"previousNode": "Start"}], "hints": [], "executionTime": 207, "executionStatus": "success", "data": {"main": [[{"json": {"id": "f-nyztATalRJ", "type": "formula", "href": "https://coda.io/apis/v1/docs/2-5DWWeFZp/formulas/f-nyztATalRJ", "name": "Test", "parent": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "Coda": [{"startTime": 1747344008879, "executionIndex": 4, "source": [{"previousNode": "Start"}], "hints": [], "executionTime": 996, "executionStatus": "success", "data": {"main": [[]]}}], "Coda12": [{"startTime": 1747344009875, "executionIndex": 5, "source": [{"previousNode": "Coda11"}], "hints": [], "executionTime": 462, "executionStatus": "success", "data": {"main": [[{"json": {"id": "table-FTj1L0rh08", "type": "table", "tableType": "view", "href": "https://coda.io/apis/v1/docs/2-5DWWeFZp/tables/table-FTj1L0rh08", "browserLink": "https://coda.io/d/_d2-5DWWeFZp#_tutable-FTj1L0rh08", "parent": {"object": true}, "parentTable": {"object": true}, "name": "View of Table 1", "displayColumn": {"object": true}, "rowCount": 121, "createdAt": "2021-02-22T08:40:15.720Z", "updatedAt": "2021-02-22T08:40:15.720Z", "viewId": "v-k0w5NxL7Wt", "sorts": ["json array"], "layout": "masterDetail"}, "pairedItem": {"item": 0}}]]}}], "Coda5": [{"startTime": 1747344010337, "executionIndex": 6, "source": [{"previousNode": "Coda4"}], "hints": [], "executionTime": 235, "executionStatus": "success", "data": {"main": [[{"json": {"id": "c-XvglOYNTU8", "type": "column", "name": "number", "href": "https://coda.io/apis/v1/docs/2-5DWWeFZp/tables/grid-lDqTU2W4nP/columns/c-XvglOYNTU8", "display": true, "format": {"object": true}, "parent": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "Coda3": [{"startTime": 1747344010572, "executionIndex": 7, "source": [{"previousNode": "Coda2"}], "hints": [], "executionTime": 2452, "executionStatus": "success", "data": {"main": [[{"json": {"id": "f-nyztATalRJ", "type": "formula", "href": "https://coda.io/apis/v1/docs/2-5DWWeFZp/formulas/f-nyztATalRJ", "name": "Test", "value": "", "parent": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "Coda13": [{"startTime": 1747344013024, "executionIndex": 8, "source": [{"previousNode": "Coda12"}], "hints": [], "executionTime": 320, "executionStatus": "success", "data": {"main": [[{"json": {"id": "c-XvglOYNTU8", "type": "column", "name": "number", "href": "https://coda.io/apis/v1/docs/2-5DWWeFZp/tables/grid-lDqTU2W4nP/columns/c-XvglOYNTU8", "display": true, "format": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "Coda6": [{"startTime": 1747344013344, "executionIndex": 9, "source": [{"previousNode": "Coda5"}], "hints": [], "executionTime": 316, "executionStatus": "success", "data": {"main": [[{"json": {"id": "i-62srMH_hOe", "number": 101, "sum": 0, "Column 3": ""}, "pairedItem": [{"item": 0}]}]]}}], "Coda14": [{"startTime": 1747344013660, "executionIndex": 10, "source": [{"previousNode": "Coda13"}], "hints": [], "executionTime": 2614, "executionStatus": "success", "data": {"main": [[{"json": {"id": "i-62srMH_hOe", "number": 101, "sum": 0, "Column 3": ""}, "pairedItem": [{"item": 0}]}]]}}], "Coda7": [{"startTime": 1747344016274, "executionIndex": 11, "source": [{"previousNode": "Coda6"}], "hints": [], "executionTime": 259, "executionStatus": "success", "data": {"main": [[{"json": {"id": "i-62srMH_hOe", "number": 101, "sum": 0, "Column 3": ""}, "pairedItem": {"item": 0}}]]}}], "Set1": [{"startTime": 1747344016533, "executionIndex": 12, "source": [{"previousNode": "Coda14"}], "hints": [], "executionTime": 0, "executionStatus": "success", "data": {"main": [[{"json": {"number": 102, "sum": 0}, "pairedItem": {"item": 0}}]]}}], "Set": [{"startTime": 1747344016533, "executionIndex": 13, "source": [{"previousNode": "Coda7"}], "hints": [], "executionTime": 0, "executionStatus": "success", "data": {"main": [[{"json": {"number": 101, "sum": 0}, "pairedItem": {"item": 0}}]]}}], "Coda15": [{"startTime": 1747344016533, "executionIndex": 14, "source": [{"previousNode": "Set1"}], "hints": [], "executionTime": 643, "executionStatus": "success", "data": {"main": [[{"json": {"number": 102, "sum": 0}, "pairedItem": {"item": 0}}]]}}], "Coda8": [{"startTime": 1747344017176, "executionIndex": 15, "source": [{"previousNode": "Set"}], "hints": [], "executionTime": 300, "executionStatus": "success", "data": {"main": [[{"json": {"number": 101, "sum": 0}, "pairedItem": {"item": 0}}]]}}], "Coda16": [{"startTime": 1747344017476, "executionIndex": 16, "source": [{"previousNode": "Coda15"}], "hints": [], "executionTime": 1373, "executionStatus": "success", "data": {"main": [[{"json": {"rowId": "i-62srMH_hOe", "columnId": "c-hTB8QwFtUS", "requestId": "mutate:df2e7520-d748-457f-ae97-81138ebc1f0c"}, "pairedItem": {"item": 0}}]]}}], "Coda10": [{"startTime": 1747344018849, "executionIndex": 17, "source": [{"previousNode": "Coda8"}], "hints": [], "executionTime": 453, "executionStatus": "success", "data": {"main": [[{"json": {"rowId": "i-62srMH_hOe", "columnId": "c-hTB8QwFtUS", "requestId": "mutate:49bd58a9-b483-4b84-8627-0fef4dfcdbf6"}, "pairedItem": {"item": 0}}]]}}], "Coda9": [{"startTime": 1747344019302, "executionIndex": 18, "source": [{"previousNode": "Coda10"}], "hints": [], "executionTime": 4629, "executionStatus": "success", "data": {"main": [[{"json": {"rowId": "i-62srMH_hOe", "columnId": "c-hTB8QwFtUS", "requestId": "mutate:49bd58a9-b483-4b84-8627-0fef4dfcdbf6"}, "pairedItem": {"item": 0}}]]}}]}, "lastNodeExecuted": "Coda9"}, "executionData": {"contextData": {}, "nodeExecutionStack": [], "metadata": {}, "waitingExecution": {}, "waitingExecutionSource": {}}}, "mode": "cli", "startedAt": "2025-05-15T21:20:04.590Z", "stoppedAt": "2025-05-15T21:20:23.931Z", "status": "running", "finished": true}