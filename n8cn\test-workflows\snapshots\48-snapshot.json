{"data": {"startData": {}, "resultData": {"runData": {"Start": [{"startTime": 1747344004021, "executionIndex": 0, "source": [], "hints": [], "executionTime": 0, "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "Asana2": [{"startTime": 1747344004021, "executionIndex": 1, "source": [{"previousNode": "Start"}], "hints": [], "executionTime": 686, "executionStatus": "success", "data": {"main": [[{"json": {"gid": "1210267332379092", "projects": ["json array"], "memberships": ["json array"], "resource_type": "task", "created_at": "2025-05-15T21:20:04.296Z", "modified_at": "2025-05-15T21:20:04.486Z", "name": "TaskTest", "notes": "", "workspace": {"object": true}, "start_at": {"object": true}, "start_on": {"object": true}, "resource_subtype": "default_task", "due_at": {"object": true}, "due_on": {"object": true}, "completed_at": {"object": true}, "assignee_status": "inbox", "completed": false, "actual_time_minutes": 0, "num_hearts": 0, "num_likes": 0, "tags": ["json array"], "assignee_section": {"object": true}, "hearted": false, "liked": false, "hearts": ["json array"], "likes": ["json array"], "permalink_url": "https://app.asana.com/1/1177253494675264/project/1199961131280920/task/1210267332379092", "assignee": {"object": true}, "parent": {"object": true}, "followers": ["json array"]}, "pairedItem": {"item": 0}}]]}}], "Asana14": [{"startTime": 1747344004707, "executionIndex": 2, "source": [{"previousNode": "Start"}], "hints": [], "executionTime": 371, "executionStatus": "success", "data": {"main": [[{"json": {"gid": "1199961026001666", "email": "<EMAIL>", "name": "nodeqa", "photo": {"object": true}, "resource_type": "user", "workspaces": ["json array"]}, "pairedItem": {"item": 0}}]]}}], "Asana": [{"startTime": 1747344005078, "executionIndex": 3, "source": [{"previousNode": "Start"}], "hints": [], "executionTime": 193, "executionStatus": "success", "data": {"main": [[{"json": {"gid": "1199961131378839", "name": "QAtest", "resource_type": "project"}, "pairedItem": {"item": 0}}]]}}], "Asana3": [{"startTime": 1747344005271, "executionIndex": 4, "source": [{"previousNode": "Asana2"}], "hints": [], "executionTime": 473, "executionStatus": "success", "data": {"main": [[{"json": {"gid": "1210267332379092", "projects": ["json array"], "memberships": ["json array"], "resource_type": "task", "created_at": "2025-05-15T21:20:04.296Z", "modified_at": "2025-05-15T21:20:05.610Z", "name": "UpdatedTestTask", "notes": "", "workspace": {"object": true}, "start_at": {"object": true}, "start_on": {"object": true}, "resource_subtype": "default_task", "due_at": {"object": true}, "due_on": {"object": true}, "completed_at": {"object": true}, "assignee_status": "inbox", "completed": false, "actual_time_minutes": 0, "num_hearts": 0, "num_likes": 0, "tags": ["json array"], "hearted": false, "liked": false, "hearts": ["json array"], "likes": ["json array"], "assignee_section": {"object": true}, "permalink_url": "https://app.asana.com/1/1177253494675264/project/1199961131280920/task/1210267332379092", "assignee": {"object": true}, "parent": {"object": true}, "followers": ["json array"]}, "pairedItem": {"item": 0}}]]}}], "Asana15": [{"startTime": 1747344005744, "executionIndex": 5, "source": [{"previousNode": "Asana14"}], "hints": [], "executionTime": 187, "executionStatus": "success", "data": {"main": [[{"json": {"gid": "1202187737680529", "name": "<PERSON><PERSON>", "resource_type": "user"}, "pairedItem": {"item": 0}}, {"json": {"gid": "1202890883228546", "name": "<PERSON><PERSON><PERSON>", "resource_type": "user"}, "pairedItem": {"item": 0}}, {"json": {"gid": "1206933450858036", "name": "<PERSON><PERSON>", "resource_type": "user"}, "pairedItem": {"item": 0}}, {"json": {"gid": "1209716676051853", "name": "<EMAIL>", "resource_type": "user"}, "pairedItem": {"item": 0}}, {"json": {"gid": "1201317194658352", "name": "<PERSON>", "resource_type": "user"}, "pairedItem": {"item": 0}}, {"json": {"gid": "1209089579490684", "name": "<PERSON>", "resource_type": "user"}, "pairedItem": {"item": 0}}, {"json": {"gid": "1209358493509784", "name": "<PERSON><PERSON>", "resource_type": "user"}, "pairedItem": {"item": 0}}, {"json": {"gid": "1183698132267547", "name": "Inte Gration", "resource_type": "user"}, "pairedItem": {"item": 0}}, {"json": {"gid": "1202307247988024", "name": "ivan", "resource_type": "user"}, "pairedItem": {"item": 0}}, {"json": {"gid": "1206344448463309", "name": "<EMAIL>", "resource_type": "user"}, "pairedItem": {"item": 0}}, {"json": {"gid": "1198750619112603", "name": "<PERSON>", "resource_type": "user"}, "pairedItem": {"item": 0}}, {"json": {"gid": "1202718728322685", "name": "<PERSON>", "resource_type": "user"}, "pairedItem": {"item": 0}}, {"json": {"gid": "1207168924369543", "name": "<EMAIL>", "resource_type": "user"}, "pairedItem": {"item": 0}}, {"json": {"gid": "1202718619090236", "name": "<PERSON>", "resource_type": "user"}, "pairedItem": {"item": 0}}, {"json": {"gid": "1199961026001666", "name": "nodeqa", "resource_type": "user"}, "pairedItem": {"item": 0}}, {"json": {"gid": "1177656661193880", "name": "<PERSON><PERSON>", "resource_type": "user"}, "pairedItem": {"item": 0}}, {"json": {"gid": "1177253503277681", "name": "<PERSON><PERSON>", "resource_type": "user"}, "pairedItem": {"item": 0}}, {"json": {"gid": "1204568752114666", "name": "Thomas <PERSON>", "resource_type": "user"}, "pairedItem": {"item": 0}}, {"json": {"gid": "1209729434313156", "name": "<EMAIL>", "resource_type": "user"}, "pairedItem": {"item": 0}}, {"json": {"gid": "1202404236960393", "name": "<PERSON>", "resource_type": "user"}, "pairedItem": {"item": 0}}]]}}], "Asana1": [{"startTime": 1747344005931, "executionIndex": 6, "source": [{"previousNode": "<PERSON><PERSON>"}], "hints": [], "executionTime": 217, "executionStatus": "success", "data": {"main": [[{"json": {"gid": "1199961131378839", "archived": false, "color": {"object": true}, "completed": false, "completed_at": {"object": true}, "created_at": "2021-02-19T14:12:58.357Z", "current_status": {"object": true}, "current_status_update": {"object": true}, "custom_fields": ["json array"], "default_access_level": "editor", "default_view": "list", "due_on": {"object": true}, "due_date": {"object": true}, "followers": ["json array"], "members": ["json array"], "minimum_access_level_for_customization": "editor", "minimum_access_level_for_sharing": "editor", "modified_at": "2025-05-13T13:32:19.367Z", "name": "QAtest", "notes": "", "owner": {"object": true}, "permalink_url": "https://app.asana.com/1/1177253494675264/project/1199961131378839", "privacy_setting": "private", "public": false, "resource_type": "project", "start_on": {"object": true}, "team": {"object": true}, "workspace": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "Asana4": [{"startTime": 1747344006148, "executionIndex": 7, "source": [{"previousNode": "Asana3"}], "hints": [], "executionTime": 862, "executionStatus": "success", "data": {"main": [[{"json": {"success": true}, "pairedItem": {"item": 0}}]]}}], "Asana5": [{"startTime": 1747344007010, "executionIndex": 8, "source": [{"previousNode": "Asana4"}], "hints": [], "executionTime": 365, "executionStatus": "success", "data": {"main": [[{"json": {"gid": "1199961131378855", "name": "✏️ Finish setting up your first project", "resource_type": "task", "resource_subtype": "default_task"}, "pairedItem": {"item": 0}}]]}}], "Asana6": [{"startTime": 1747344007375, "executionIndex": 9, "source": [{"previousNode": "Asana5"}], "hints": [], "executionTime": 209, "executionStatus": "success", "data": {"main": [[{"json": {"gid": "1210267332379092", "actual_time_minutes": {"object": true}, "assignee": {"object": true}, "assignee_status": "inbox", "assignee_section": {"object": true}, "completed": false, "completed_at": {"object": true}, "created_at": "2025-05-15T21:20:04.296Z", "due_at": {"object": true}, "due_on": {"object": true}, "followers": ["json array"], "hearted": false, "hearts": ["json array"], "liked": false, "likes": ["json array"], "memberships": ["json array"], "modified_at": "2025-05-15T21:20:06.736Z", "name": "UpdatedTestTask", "notes": "", "num_hearts": 0, "num_likes": 0, "parent": {"object": true}, "permalink_url": "https://app.asana.com/1/1177253494675264/project/1199961131378839/task/1210267332379092", "projects": ["json array"], "resource_type": "task", "start_at": {"object": true}, "start_on": {"object": true}, "tags": ["json array"], "resource_subtype": "default_task", "workspace": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "Asana8": [{"startTime": 1747344007584, "executionIndex": 10, "source": [{"previousNode": "Asana6"}], "hints": [], "executionTime": 600, "executionStatus": "success", "data": {"main": [[{"json": {"gid": "1210267334811850", "resource_type": "story", "created_at": "2025-05-15T21:20:07.929Z", "num_hearts": 0, "resource_subtype": "comment_added", "is_edited": false, "text": "TestTaskComment", "type": "comment", "is_pinned": false, "source": "api", "num_likes": 0, "liked": false, "hearted": false, "created_by": {"object": true}, "hearts": ["json array"], "likes": ["json array"], "previews": ["json array"], "is_editable": true, "target": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "Asana10": [{"startTime": 1747344008184, "executionIndex": 11, "source": [{"previousNode": "Asana6"}], "hints": [], "executionTime": 709, "executionStatus": "success", "data": {"main": [[{"json": {"success": true}, "pairedItem": {"item": 0}}]]}}], "Asana12": [{"startTime": 1747344008893, "executionIndex": 12, "source": [{"previousNode": "Asana6"}], "hints": [], "executionTime": 471, "executionStatus": "success", "data": {"main": [[{"json": {"success": true}, "pairedItem": {"item": 0}}]]}}], "Asana16": [{"startTime": 1747344009364, "executionIndex": 13, "source": [{"previousNode": "Asana6"}], "hints": [], "executionTime": 1016, "executionStatus": "success", "data": {"main": [[{"json": {"gid": "1210267364252262", "projects": ["json array"], "memberships": ["json array"], "resource_type": "task", "created_at": "2025-05-15T21:20:09.876Z", "modified_at": "2025-05-15T21:20:10.148Z", "name": "SubTestTask", "notes": "", "assignee": {"object": true}, "start_at": {"object": true}, "start_on": {"object": true}, "resource_subtype": "default_task", "due_at": {"object": true}, "due_on": {"object": true}, "completed_at": {"object": true}, "assignee_status": "upcoming", "completed": false, "actual_time_minutes": 0, "workspace": {"object": true}, "num_hearts": 0, "num_likes": 0, "hearted": false, "liked": false, "hearts": ["json array"], "likes": ["json array"], "parent": {"object": true}, "tags": ["json array"], "permalink_url": "https://app.asana.com/1/1177253494675264/project/1199961131378839/task/1210267364252262", "followers": ["json array"]}, "pairedItem": {"item": 0}}]]}}], "Asana9": [{"startTime": 1747344010380, "executionIndex": 14, "source": [{"previousNode": "Asana8"}], "hints": [], "executionTime": 742, "executionStatus": "success", "data": {"main": [[{"json": {"success": true}, "pairedItem": {"item": 0}}]]}}], "Asana11": [{"startTime": 1747344011122, "executionIndex": 15, "source": [{"previousNode": "Asana10"}], "hints": [], "executionTime": 584, "executionStatus": "success", "data": {"main": [[{"json": {"success": true}, "pairedItem": {"item": 0}}]]}}], "Asana13": [{"startTime": 1747344011706, "executionIndex": 16, "source": [{"previousNode": "Asana12"}], "hints": [], "executionTime": 598, "executionStatus": "success", "data": {"main": [[{"json": {"success": true}, "pairedItem": {"item": 0}}]]}}], "Asana17": [{"startTime": 1747344012305, "executionIndex": 17, "source": [{"previousNode": "Asana16"}], "hints": [], "executionTime": 451, "executionStatus": "success", "data": {"main": [[{"json": {"gid": "1210267364252262", "name": "SubTestTask", "resource_type": "task", "resource_subtype": "default_task"}, "pairedItem": {"item": 0}}]]}}], "Asana7": [{"startTime": 1747344012756, "executionIndex": 18, "source": [{"previousNode": "Asana17"}], "hints": [], "executionTime": 573, "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}]}, "lastNodeExecuted": "Asana7"}, "executionData": {"contextData": {}, "nodeExecutionStack": [], "metadata": {}, "waitingExecution": {}, "waitingExecutionSource": {}}}, "mode": "cli", "startedAt": "2025-05-15T21:20:04.020Z", "stoppedAt": "2025-05-15T21:20:13.329Z", "status": "running", "finished": true}