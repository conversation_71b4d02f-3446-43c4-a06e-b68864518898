#!/bin/bash
set -eo pipefail

# 导入环境变量
source /home/<USER>/.env

# 错误处理函数
handle_error() {
    echo "错误发生在第 $1 行"
    exit 1
}
trap 'handle_error $LINENO' ERR

# 等待数据库服务就绪的函数
wait_for_database() {
    local host=$1
    local port=$2
    local timeout=${3:-30}

    echo "等待数据库 $host:$port 就绪..."
    local end=$((SECONDS + timeout))

    while [ $SECONDS -lt $end ]; do
        if timeout 5 bash -c "</dev/tcp/$host/$port" >/dev/null 2>&1; then
            echo "数据库已就绪"
            return 0
        fi
        echo "尝试连接数据库 $host:$port..."
        sleep 2
    done

    echo "数据库连接超时"
    exit 1
}

# 检查企业版功能状态
check_enterprise_features() {
    echo "🚀 N8N ENTERPRISE MOCK ENABLED"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo "All enterprise features are unlocked for testing!"
    echo ""
    echo "Enterprise Features Available:"
    echo "✅ SAML单点登录"
    echo "✅ LDAP/Active Directory集成"
    echo "✅ 高级权限管理"
    echo "✅ 工作流和凭证共享"
    echo "✅ 项目管理"
    echo "✅ Git源代码控制"
    echo "✅ 环境变量管理"
    echo "✅ 外部密钥管理"
    echo "✅ 工作流版本历史"
    echo "✅ AI助手功能"
    echo "✅ 多主实例高可用"
    echo "✅ 日志流式传输"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo ""
}

# 主流程
main() {
    current_time=$(date +"%Y-%m-%d %H:%M:%S")
    echo "Starting N8N Enterprise Edition at $current_time"

    # 显示企业版功能状态
    check_enterprise_features

    # 输出配置信息
    echo "Database Configuration:"
    echo "Host: ${DB_POSTGRESDB_HOST}"
    echo "Port: ${DB_POSTGRESDB_PORT}"
    echo "User: ${DB_POSTGRESDB_USER}"
    echo "Database: ${DB_POSTGRESDB_DATABASE}"
    echo "Type: ${DB_TYPE}"
    echo ""

    # 等待数据库就绪
    wait_for_database "${DB_POSTGRESDB_HOST}" "${DB_POSTGRESDB_PORT}"
    echo ""

    # 设置 N8N 环境变量（如果配置文件存在）
    if [ -f "/home/<USER>/n8n/config/n8n_env.sh" ]; then
        source /home/<USER>/n8n/config/n8n_env.sh
    fi

    echo ""
    echo "Starting n8n Enterprise Edition..."
    echo "Access your n8n instance at: ${WEBHOOK_URL}"
    echo ""
    exec n8n start
}

# 执行主流程
main "$@"