{"createdAt": "2021-03-10T10:32:50.903Z", "updatedAt": "2021-03-10T10:32:50.903Z", "id": "116", "name": "Ghost:Post(Admin API):create update get getAll delete:Post(Content API):getAll get", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300], "id": "96a1cf58-f3dc-46ea-a07a-5202bffee113"}, {"parameters": {"source": "adminApi", "operation": "create", "title": "=PostTitle{{Date.now()}}", "content": "=Post Content written at {{Date.now()}}", "additionalFields": {}}, "name": "Ghost", "type": "n8n-nodes-base.ghost", "typeVersion": 1, "position": [550, 300], "credentials": {"ghostAdminApi": {"id": "83", "name": "Ghost Admin api creds"}}, "id": "6c09ea13-41e8-4704-b357-4390b14e7255"}, {"parameters": {"source": "adminApi", "operation": "update", "postId": "={{$node[\"Ghost\"].json[\"id\"]}}", "updateFields": {"title": "=UpdateTitle{{Date.now()}}"}}, "name": "Ghost1", "type": "n8n-nodes-base.ghost", "typeVersion": 1, "position": [700, 300], "credentials": {"ghostAdminApi": {"id": "83", "name": "Ghost Admin api creds"}}, "id": "57fab43f-0013-4250-bcd4-1318f9371097"}, {"parameters": {"source": "adminApi", "by": "id", "identifier": "={{$node[\"Ghost\"].json[\"id\"]}}", "options": {}}, "name": "Ghost2", "type": "n8n-nodes-base.ghost", "typeVersion": 1, "position": [850, 300], "credentials": {"ghostAdminApi": {"id": "83", "name": "Ghost Admin api creds"}}, "id": "e032227a-b97e-4b60-8b9e-9061c2d7e279"}, {"parameters": {"source": "adminApi", "operation": "getAll", "limit": 1, "options": {}}, "name": "Ghost3", "type": "n8n-nodes-base.ghost", "typeVersion": 1, "position": [1000, 300], "credentials": {"ghostAdminApi": {"id": "83", "name": "Ghost Admin api creds"}}, "id": "6150282c-3d31-4387-8dbb-74730ada47aa"}, {"parameters": {"operation": "getAll", "limit": 1, "options": {}}, "name": "Ghost4", "type": "n8n-nodes-base.ghost", "typeVersion": 1, "position": [1150, 450], "credentials": {"ghostContentApi": {"id": "82", "name": "Ghost Content creds"}}, "id": "488558ee-8ef5-460b-ae0e-31e74ce25d9c"}, {"parameters": {"by": "id", "identifier": "={{$node[\"Ghost4\"].json[\"id\"]}}", "options": {}}, "name": "Ghost5", "type": "n8n-nodes-base.ghost", "typeVersion": 1, "position": [1300, 450], "credentials": {"ghostContentApi": {"id": "82", "name": "Ghost Content creds"}}, "id": "f0f53fe7-b17f-45fc-bfa5-b7db642a22a9"}, {"parameters": {"source": "adminApi", "operation": "delete", "postId": "={{$node[\"Ghost\"].json[\"id\"]}}"}, "name": "Ghost6", "type": "n8n-nodes-base.ghost", "typeVersion": 1, "position": [1450, 300], "credentials": {"ghostAdminApi": {"id": "83", "name": "Ghost Admin api creds"}}, "id": "83c79ebf-21b0-4d8b-be7e-9e7002015990"}], "connections": {"Ghost": {"main": [[{"node": "Ghost1", "type": "main", "index": 0}]]}, "Ghost1": {"main": [[{"node": "Ghost2", "type": "main", "index": 0}]]}, "Ghost2": {"main": [[{"node": "Ghost3", "type": "main", "index": 0}]]}, "Ghost3": {"main": [[{"node": "Ghost4", "type": "main", "index": 0}]]}, "Ghost4": {"main": [[{"node": "Ghost5", "type": "main", "index": 0}]]}, "Ghost5": {"main": [[{"node": "Ghost6", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "Ghost", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}