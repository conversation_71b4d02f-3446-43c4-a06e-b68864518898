{"data": {"startData": {}, "resultData": {"runData": {"When clicking \"Test workflow\"": [{"startTime": 1747343997489, "executionIndex": 0, "source": [], "hints": [], "executionTime": 0, "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "OpenAI Chat Model1": [{"startTime": 1747343997494, "executionTime": 808, "executionIndex": 2, "executionStatus": "success", "source": [{"previousNode": "Open AI Chat - Output Parsing", "previousNodeRun": 0}], "data": {"ai_languageModel": [[{"json": {"response": {"object": true}, "tokenUsage": {"object": true}}}]]}, "inputOverride": {"ai_languageModel": [[{"json": {"messages": ["Human: What are the top 5 states of US by population?\nYou must format your output as a JSON value that adheres to a given \"JSON Schema\" instance.\n\n\"JSON Schema\" is a declarative language that allows you to annotate and validate JSON documents.\n\nFor example, the example \"JSON Schema\" instance {{\"properties\": {{\"foo\": {{\"description\": \"a list of test words\", \"type\": \"array\", \"items\": {{\"type\": \"string\"}}}}}}, \"required\": [\"foo\"]}}}}\nwould match an object with one required property, \"foo\". The \"type\" property specifies \"foo\" must be an \"array\", and the \"description\" property semantically describes it as \"a list of test words\". The items within \"foo\" must be strings.\nThus, the object {{\"foo\": [\"bar\", \"baz\"]}} is a well-formatted instance of this example \"JSON Schema\". The object {{\"properties\": {{\"foo\": [\"bar\", \"baz\"]}}}} is not well-formatted.\n\nYour output will be parsed and type-checked according to the provided schema instance, so make sure all fields in your output match the schema exactly and there are no trailing commas!\n\nHere is the JSON Schema instance your output must adhere to. Include the enclosing markdown codeblock:\n```json\n{\"type\":\"object\",\"properties\":{\"output\":{\"type\":\"object\",\"properties\":{\"state\":{\"type\":\"string\"},\"cities\":{\"type\":\"array\",\"items\":{\"type\":\"string\"}}},\"additionalProperties\":false}},\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}\n```\n"], "estimatedTokens": 336, "options": {"openai_api_key": {"lc": 1, "type": "secret", "id": ["OPENAI_API_KEY"]}, "model": "gpt-4o-mini", "temperature": 0, "timeout": 60000, "max_retries": 2, "configuration": {"baseURL": "https://api.openai.com/v1"}, "model_kwargs": {}}}}]]}, "metadata": {"subRun": [{"node": "OpenAI Chat Model1", "runIndex": 0}]}}], "Structured Output Parser1": [{"startTime": 1747343998303, "executionTime": 2, "executionIndex": 3, "executionStatus": "success", "source": [{"previousNode": "Open AI Chat - Output Parsing", "previousNodeRun": 0}], "data": {"ai_outputParser": [[{"json": {"action": "parse", "response": {"object": true}}}]]}, "inputOverride": {"ai_outputParser": [[{"json": {"action": "parse", "text": "```json\n{\"output\":{\"state\":\"California\",\"cities\":[\"Los Angeles\",\"San Francisco\",\"San Diego\",\"San Jose\",\"Sacramento\"]}}\n```"}}]]}, "metadata": {"subRun": [{"node": "Structured Output Parser1", "runIndex": 0}]}}], "Open AI Chat - Output Parsing": [{"startTime": 1747343997489, "executionIndex": 1, "source": [{"previousNode": "When clicking \"Test workflow\""}], "hints": [], "executionTime": 817, "executionStatus": "success", "data": {"main": [[{"json": {"output": {"object": true}}, "pairedItem": {"item": 0}}]]}}]}, "lastNodeExecuted": "Open AI Chat - Output Parsing"}, "executionData": {"contextData": {}, "nodeExecutionStack": [], "metadata": {"OpenAI Chat Model1": [{"subRun": [{"node": "OpenAI Chat Model1", "runIndex": 0}]}], "Structured Output Parser1": [{"subRun": [{"node": "Structured Output Parser1", "runIndex": 0}]}]}, "waitingExecution": {}, "waitingExecutionSource": {}}}, "mode": "cli", "startedAt": "2025-05-15T21:19:57.489Z", "stoppedAt": "2025-05-15T21:19:58.306Z", "status": "running", "finished": true}