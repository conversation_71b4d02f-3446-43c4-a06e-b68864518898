#!/bin/bash

# N8N 企业版环境配置
# 此文件包含 n8n 企业版的特殊环境变量配置

echo "Loading N8N Enterprise Environment Configuration..."

# 企业版核心配置
export N8N_ENTERPRISE_MOCK=true
export N8N_DEFAULT_LOCALE=zh-CN
export NODE_ENV=development

# 企业功能启用
export N8N_SAML_ENABLED=true
export N8N_LDAP_ENABLED=true
export N8N_LOG_STREAMING_ENABLED=true
export N8N_VARIABLES_ENABLED=true
export N8N_SOURCE_CONTROL_ENABLED=true
export N8N_EXTERNAL_SECRETS_ENABLED=true
export N8N_WORKFLOW_HISTORY_ENABLED=true
export N8N_AI_ASSISTANT_ENABLED=true

# 用户管理相关
export N8N_USER_MANAGEMENT_JWT_SECRET=enterprise-mock-jwt-secret
export N8N_USER_MANAGEMENT_DISABLED=false

# 许可证相关（企业版模拟）
export N8N_LICENSE_AUTO_RENEW_ENABLED=false
export N8N_LICENSE_SERVER_URL=""
export N8N_LICENSE_CERT=""

# 禁用遥测和通知
export N8N_DIAGNOSTICS_ENABLED=false
export N8N_VERSION_NOTIFICATIONS_ENABLED=false

# AI Assistant 配置
export N8N_AI_ASSISTANT_BASE_URL="https://api.n8n.io"

# 调试配置
export N8N_LOG_LEVEL=info
export N8N_DEBUG=false

# 安全配置
export N8N_SECURE_COOKIE=false
export N8N_COOKIE_SAME_SITE_POLICY=lax

# 工作流配置
export EXECUTIONS_PROCESS=main
export EXECUTIONS_MODE=regular
export QUEUE_BULL_REDIS_HOST=localhost
export QUEUE_BULL_REDIS_PORT=6379

# Webhook 配置
export WEBHOOK_URL=${WEBHOOK_URL:-"https://你的hf账户名-空间名.hf.space/"}

# 文件存储配置
export N8N_BINARY_DATA_MODE=filesystem
export N8N_DEFAULT_BINARY_DATA_MODE=filesystem

# 性能配置
export N8N_PAYLOAD_SIZE_MAX=256
export N8N_METRICS=true
export QUEUE_HEALTH_CHECK_ACTIVE=true

echo "N8N Enterprise Environment Configuration Loaded Successfully!"
echo "Enterprise Mock: ${N8N_ENTERPRISE_MOCK}"
echo "Default Locale: ${N8N_DEFAULT_LOCALE}"
echo "Node Environment: ${NODE_ENV}"
