{"data": {"startData": {}, "resultData": {"runData": {"Start": [{"startTime": 1710331886877, "executionTime": 0, "source": [], "executionStatus": "success", "data": {"main": [[{"json": {}, "pairedItem": {"item": 0}}]]}}], "PagerDuty": [{"startTime": 1710331886877, "executionTime": 1389, "source": [{"previousNode": "Start"}], "executionStatus": "success", "data": {"main": [[{"json": {"incident_number": 1722, "title": "Test", "description": "Test", "created_at": "2024-03-13T12:11:28Z", "updated_at": "2024-03-13T12:11:28Z", "status": "triggered", "incident_key": "d3b8964acebb45b4a91944a06bc89930", "service": {"object": true}, "assignments": ["json array"], "assigned_via": "escalation_policy", "last_status_change_at": "2024-03-13T12:11:28Z", "resolved_at": {"object": true}, "first_trigger_log_entry": {"object": true}, "alert_counts": {"object": true}, "is_mergeable": true, "escalation_policy": {"object": true}, "teams": ["json array"], "impacted_services": ["json array"], "pending_actions": ["json array"], "acknowledgements": ["json array"], "basic_alert_grouping": {"object": true}, "alert_grouping": {"object": true}, "last_status_change_by": {"object": true}, "incidents_responders": ["json array"], "responder_requests": ["json array"], "subscriber_requests": ["json array"], "urgency": "high", "id": "Q3Z252030C2VEG", "type": "incident", "summary": "[#1722] Test", "self": "https://api.pagerduty.com/incidents/Q3Z252030C2VEG", "html_url": "https://dev-nodeqan8n.pagerduty.com/incidents/Q3Z252030C2VEG"}, "pairedItem": {"item": 0}}]]}}], "PagerDuty7": [{"startTime": 1710331888266, "executionTime": 1108, "source": [{"previousNode": "Start"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": "R4CLO6G4XRB6395SM2S6WLWH4A", "type": "assign_log_entry", "summary": "Assigned to Node Qa.", "self": "https://api.pagerduty.com/log_entries/R4CLO6G4XRB6395SM2S6WLWH4A", "html_url": {"object": true}, "created_at": "2024-03-13T12:11:28Z", "agent": {"object": true}, "channel": {"object": true}, "service": {"object": true}, "incident": {"object": true}, "teams": ["json array"], "contexts": ["json array"]}, "pairedItem": {"item": 0}}]]}}], "PagerDuty6": [{"startTime": 1710331889374, "executionTime": 861, "source": [{"previousNode": "Start"}], "executionStatus": "success", "data": {"main": [[{"json": {"name": "Node Qa", "email": "<EMAIL>", "time_zone": "Europe/Berlin", "color": "purple", "avatar_url": "https://secure.gravatar.com/avatar/c81277605b129fdafaacede5ae34e07c.png?d=mm&r=PG", "billed": true, "role": "owner", "description": {"object": true}, "invitation_sent": false, "job_title": {"object": true}, "teams": ["json array"], "contact_methods": ["json array"], "notification_rules": ["json array"], "personal_web_cal_url": "webcal://dev-nodeqan8n.pagerduty.com/private/effe61535b688813e51b31cd68f98cf54428fe2848b86672bcff8689c9900c5a/feed", "personal_http_cal_url": "https://dev-nodeqan8n.pagerduty.com/private/effe61535b688813e51b31cd68f98cf54428fe2848b86672bcff8689c9900c5a/feed", "coordinated_incidents": ["json array"], "id": "PT0VVWO", "type": "user", "summary": "Node Qa", "self": "https://api.pagerduty.com/users/PT0VVWO", "html_url": "https://dev-nodeqan8n.pagerduty.com/users/PT0VVWO"}, "pairedItem": {"item": 0}}]]}}], "PagerDuty1": [{"startTime": 1710331890235, "executionTime": 919, "source": [{"previousNode": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "executionStatus": "success", "data": {"main": [[{"json": {"incident_number": 1722, "title": "Test", "description": "Test", "created_at": "2024-03-13T12:11:28Z", "updated_at": "2024-03-13T12:11:28Z", "status": "triggered", "incident_key": "d3b8964acebb45b4a91944a06bc89930", "service": {"object": true}, "assignments": ["json array"], "assigned_via": "escalation_policy", "last_status_change_at": "2024-03-13T12:11:28Z", "resolved_at": {"object": true}, "first_trigger_log_entry": {"object": true}, "alert_counts": {"object": true}, "is_mergeable": true, "escalation_policy": {"object": true}, "teams": ["json array"], "impacted_services": ["json array"], "pending_actions": ["json array"], "acknowledgements": ["json array"], "basic_alert_grouping": {"object": true}, "alert_grouping": {"object": true}, "last_status_change_by": {"object": true}, "incidents_responders": ["json array"], "responder_requests": ["json array"], "subscriber_requests": ["json array"], "urgency": "high", "id": "Q3Z252030C2VEG", "type": "incident", "summary": "[#1722] Test", "self": "https://api.pagerduty.com/incidents/Q3Z252030C2VEG", "html_url": "https://dev-nodeqan8n.pagerduty.com/incidents/Q3Z252030C2VEG"}, "pairedItem": {"item": 0}}]]}}], "Sleep 0.5 second1": [{"startTime": 1710331891154, "executionTime": 503, "source": [{"previousNode": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "executionStatus": "success", "data": {"main": [[{"json": {"incident_number": 1722, "title": "Test", "description": "Test", "created_at": "2024-03-13T12:11:28Z", "updated_at": "2024-03-13T12:11:28Z", "status": "triggered", "incident_key": "d3b8964acebb45b4a91944a06bc89930", "service": {"object": true}, "assignments": ["json array"], "assigned_via": "escalation_policy", "last_status_change_at": "2024-03-13T12:11:28Z", "resolved_at": {"object": true}, "first_trigger_log_entry": {"object": true}, "alert_counts": {"object": true}, "is_mergeable": true, "escalation_policy": {"object": true}, "teams": ["json array"], "impacted_services": ["json array"], "pending_actions": ["json array"], "acknowledgements": ["json array"], "basic_alert_grouping": {"object": true}, "alert_grouping": {"object": true}, "last_status_change_by": {"object": true}, "incidents_responders": ["json array"], "responder_requests": ["json array"], "subscriber_requests": ["json array"], "urgency": "high", "id": "Q3Z252030C2VEG", "type": "incident", "summary": "[#1722] Test", "self": "https://api.pagerduty.com/incidents/Q3Z252030C2VEG", "html_url": "https://dev-nodeqan8n.pagerduty.com/incidents/Q3Z252030C2VEG"}, "pairedItem": {"item": 0}, "index": 0}]]}}], "PagerDuty4": [{"startTime": 1710331891657, "executionTime": 1169, "source": [{"previousNode": "PagerDuty1"}], "executionStatus": "success", "data": {"main": [[{"json": {"note": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "PagerDuty5": [{"startTime": 1710331892826, "executionTime": 843, "source": [{"previousNode": "PagerDuty1"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": "POGZPU2", "user": {"object": true}, "content": "Simple note for an incident", "created_at": "2024-03-13T13:11:32+01:00", "channel": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "PagerDuty2": [{"startTime": 1710331893669, "executionTime": 1144, "source": [{"previousNode": "PagerDuty1"}], "executionStatus": "success", "data": {"main": [[{"json": {"incident_number": 1722, "title": "Test", "description": "Test", "created_at": "2024-03-13T12:11:28Z", "updated_at": "2024-03-13T12:11:34Z", "status": "acknowledged", "incident_key": "d3b8964acebb45b4a91944a06bc89930", "service": {"object": true}, "assignments": ["json array"], "assigned_via": "escalation_policy", "last_status_change_at": "2024-03-13T12:11:34Z", "resolved_at": {"object": true}, "first_trigger_log_entry": {"object": true}, "alert_counts": {"object": true}, "is_mergeable": true, "escalation_policy": {"object": true}, "teams": ["json array"], "impacted_services": ["json array"], "pending_actions": ["json array"], "acknowledgements": ["json array"], "basic_alert_grouping": {"object": true}, "alert_grouping": {"object": true}, "last_status_change_by": {"object": true}, "incidents_responders": ["json array"], "responder_requests": ["json array"], "subscriber_requests": ["json array"], "urgency": "high", "id": "Q3Z252030C2VEG", "type": "incident", "summary": "[#1722] Test", "self": "https://api.pagerduty.com/incidents/Q3Z252030C2VEG", "html_url": "https://dev-nodeqan8n.pagerduty.com/incidents/Q3Z252030C2VEG"}, "pairedItem": {"item": 0}}]]}}], "PagerDuty8": [{"startTime": 1710331894813, "executionTime": 871, "source": [{"previousNode": "Sleep 0.5 second1"}], "executionStatus": "success", "data": {"main": [[{"json": {"id": "RQHI382RHNLDBDSY2RC947RRYM", "type": "trigger_log_entry", "summary": "Triggered through the website.", "self": "https://api.pagerduty.com/log_entries/RQHI382RHNLDBDSY2RC947RRYM", "html_url": "https://dev-nodeqan8n.pagerduty.com/incidents/Q3Z252030C2VEG/log_entries/RQHI382RHNLDBDSY2RC947RRYM", "created_at": "2024-03-13T12:11:28Z", "agent": {"object": true}, "channel": {"object": true}, "service": {"object": true}, "incident": {"object": true}, "teams": ["json array"], "contexts": ["json array"], "event_details": {"object": true}}, "pairedItem": {"item": 0}}]]}}], "PagerDuty3": [{"startTime": 1710331895685, "executionTime": 940, "source": [{"previousNode": "PagerDuty2"}], "executionStatus": "success", "data": {"main": [[{"json": {"incident_number": 1673, "title": "Test", "description": "Test", "created_at": "2024-02-14T02:09:08Z", "updated_at": "2024-02-14T02:09:12Z", "status": "acknowledged", "incident_key": "d11eee79ee8048e99aa37675daf7a621", "service": {"object": true}, "assignments": ["json array"], "assigned_via": "escalation_policy", "last_status_change_at": "2024-02-14T02:09:12Z", "resolved_at": {"object": true}, "first_trigger_log_entry": {"object": true}, "alert_counts": {"object": true}, "is_mergeable": true, "escalation_policy": {"object": true}, "teams": ["json array"], "pending_actions": ["json array"], "acknowledgements": ["json array"], "basic_alert_grouping": {"object": true}, "alert_grouping": {"object": true}, "last_status_change_by": {"object": true}, "incidents_responders": ["json array"], "responder_requests": ["json array"], "subscriber_requests": ["json array"], "urgency": "high", "id": "Q3SVCZJPSB89X4", "type": "incident", "summary": "[#1673] Test", "self": "https://api.pagerduty.com/incidents/Q3SVCZJPSB89X4", "html_url": "https://dev-nodeqan8n.pagerduty.com/incidents/Q3SVCZJPSB89X4"}, "pairedItem": {"item": 0}}]]}}]}, "lastNodeExecuted": "PagerDuty3"}, "executionData": {"contextData": {}, "nodeExecutionStack": [], "metadata": {}, "waitingExecution": {}, "waitingExecutionSource": {}}}, "mode": "cli", "startedAt": "2024-03-13T12:11:26.876Z", "stoppedAt": "2024-03-13T12:11:36.625Z", "status": "running", "finished": true}